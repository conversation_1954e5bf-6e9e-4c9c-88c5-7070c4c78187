// /api/auth/verify-otp.ts
import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { serialize } from 'cookie';
import jwt from 'jsonwebtoken';

const pool = mysql.createPool({
  host: process.env.MYSQL_HOST!,
  user: process.env.MYSQL_USER!,
  password: process.env.MYSQL_PASSWORD!,
  database: process.env.MYSQL_DATABASE!,
  waitForConnections: true,
  connectionLimit: 10,
});

const JWT_SECRET = process.env.JWT_SECRET || '705c083b6e26795fa47f2ff56c712171db00bd86c557d4de6df79e157a3d3211'; // Use .env for production
const JWT_EXPIRES = '7d'; // JWT expiry

export async function POST(request: NextRequest) {
  const { phoneNumber, otp } = (await request.json()) as { phoneNumber: string; otp: string };

  // Check OTP validity and expiry
  const [otpRows] = await pool.execute(
    'SELECT * FROM otps WHERE phone = ? AND otp = ? AND expires_at > NOW()',
    [phoneNumber, otp]
  );

  if (!Array.isArray(otpRows) || otpRows.length === 0) {
    return NextResponse.json({ error: 'Invalid or expired OTP' }, { status: 401 });
  }

  // Fetch user info
  const [userRows] = await pool.execute(
    'SELECT id, username FROM users WHERE email = ?',
    [phoneNumber]
  );

  if (!Array.isArray(userRows) || userRows.length === 0) {
    return NextResponse.json({ error: 'User not found' }, { status: 404 });
  }

  const user = userRows[0];

  // Create JWT token
  const token = jwt.sign(
    {
      userId: user.id,
      username: user.username,
      phoneNumber,
    },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES }
  );

  // Set token in httpOnly, secure cookie
  const cookie = serialize('token', token, {
    path: '/',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    sameSite: 'lax',
  });

  return new NextResponse(
    JSON.stringify({ success: true, userId: user.id, username: user.username }),
    {
      status: 200,
      headers: { 'Set-Cookie': cookie },
    }
  );
}
