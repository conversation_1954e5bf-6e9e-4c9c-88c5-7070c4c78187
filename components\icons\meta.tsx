import * as React from "react"
import type { SVGProps } from "react"

export default function MetaIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={192}
      height={192}
      viewBox="0 0 192 192"
      fill="none"
      {...props}
    >
      <g clipPath="url(#meta)">
        <path
          fill="url(#b)"
          d="M55.176 32h-.192l-.248 20.92h.176c13.72 0 24.368 10.856 47.52 49.968l1.4 2.376.096.16 12.96-19.504-.096-.152a390 390 0 0 0-8.784-13.728 224 224 0 0 0-9.4-13.032C83.304 39.456 70.504 32 55.176 32"
        />
        <path
          fill="url(#c)"
          d="M54.984 32C39.6 32.08 25.976 42.064 16.16 57.36l-.08.136 18.032 9.848.088-.136c5.744-8.664 12.88-14.192 20.544-14.28h.168L55.168 32z"
        />
        <path
          fill="url(#d)"
          d="m16.152 57.36-.088.136C9.6 67.576 4.784 79.96 2.192 93.312l-.04.176 20.272 4.8.032-.176c2.16-11.736 6.288-22.624 11.648-30.76l.088-.136z"
        />
        <path
          fill="url(#e)"
          d="m22.456 98.112-20.264-4.8-.04.176A119 119 0 0 0 0 115.776v.184l20.784 1.864v-.184a100.7 100.7 0 0 1 1.68-19.52z"
        />
        <path
          fill="url(#f)"
          d="M21.416 124.296a44 44 0 0 1-.632-6.504v-.176L0 115.744v.192a71 71 0 0 0 1.168 13.216l20.28-4.68z"
        />
        <path
          fill="url(#g)"
          d="M26.16 135.12c-2.272-2.48-3.872-6.048-4.712-10.624l-.032-.168-20.28 4.68.032.168c1.536 8.08 4.544 14.8 8.848 19.896l.112.136 16.144-13.96c-.04-.042-.072-.085-.112-.128"
        />
        <path
          fill="#0082FB"
          d="M86.24 77.232c-12.224 18.8-19.632 30.6-19.632 30.6-16.28 25.6-21.912 31.336-30.968 31.336a12.37 12.37 0 0 1-9.488-4.064l-16.136 13.952.112.136C16.08 156.144 24.464 160 34.848 160c15.704 0 26.992-7.424 47.072-42.64l14.128-25.04a330 330 0 0 0-9.808-15.088"
        />
        <path
          fill="url(#h)"
          d="m108.016 47.568-.128.128c-3.2 3.44-6.288 7.264-9.28 11.328 3.024 3.864 6.144 8.192 9.4 13.04 3.84-5.944 7.424-10.76 10.936-14.456l.128-.128z"
        />
        <path
          fill="#0082FB"
          d="M167.344 45.704C158.824 37.064 148.664 32 137.8 32c-11.456 0-21.096 6.296-29.784 15.552l-.128.128 11.056 9.92.128-.136c5.72-5.976 11.264-8.96 17.408-8.96 6.608 0 12.8 3.12 18.16 8.6l.12.128 12.712-11.4z"
        />
        <path
          fill="url(#i)"
          d="M191.984 113c-.48-27.736-10.16-52.528-24.512-67.168l-.128-.128-12.704 11.392.12.128c10.8 11.136 18.216 31.84 18.888 55.768v.184h18.336z"
        />
        <path
          fill="url(#j)"
          d="M191.984 113.2v-.184h-18.336v.176c.032 1.12.048 2.256.048 3.392 0 6.52-.968 11.792-2.944 15.6l-.088.176 13.664 14.256.104-.16c4.96-7.68 7.568-18.344 7.568-31.28 0-.664 0-1.32-.016-1.976"
        />
        <path
          fill="url(#k)"
          d="m170.752 132.16-.088.16c-1.712 3.216-4.152 5.36-7.336 6.296l6.224 19.696a28 28 0 0 0 3.504-1.456 28.46 28.46 0 0 0 10.928-9.744l.352-.52.096-.16z"
        />
        <path
          fill="url(#l)"
          d="M159.36 139.144c-2.096 0-3.936-.312-5.744-1.12l-6.384 20.176c3.592 1.224 7.416 1.776 11.68 1.776 3.936 0 7.544-.584 10.816-1.72l-6.24-19.696c-1.336.4-2.72.6-4.128.584"
        />
        <path
          fill="url(#m)"
          d="m146.584 132.272-.112-.136-14.688 15.312.128.136c5.096 5.456 9.968 8.84 15.496 10.696l6.376-20.16c-2.328-1-4.584-2.824-7.2-5.848"
        />
        <path
          fill="url(#n)"
          d="M146.472 132.12c-4.4-5.136-9.856-13.696-18.424-27.52L116.88 85.912l-.088-.16-12.96 19.504.096.16 7.912 13.344c7.672 12.88 13.92 22.192 19.944 28.68l.128.128 14.672-15.312z"
        />
      </g>
      <defs>
        <linearGradient
          id="b"
          x1={4771.89}
          x2={917.775}
          y1={6581.35}
          y2={1514.83}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.001} stopColor="#0867DF" />
          <stop offset={0.454} stopColor="#0668E1" />
          <stop offset={0.859} stopColor="#0064E0" />
        </linearGradient>
        <linearGradient
          id="c"
          x1={863.117}
          x2={3613.1}
          y1={2713.69}
          y2={620.668}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.132} stopColor="#0064DF" />
          <stop offset={0.999} stopColor="#0064E0" />
        </linearGradient>
        <linearGradient
          id="d"
          x1={1228.1}
          x2={2349.22}
          y1={3705.15}
          y2={874.403}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.015} stopColor="#0072EC" />
          <stop offset={0.688} stopColor="#0064DF" />
        </linearGradient>
        <linearGradient
          id="e"
          x1={1056.53}
          x2={1193.29}
          y1={2304.05}
          y2={480.888}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.073} stopColor="#007CF6" />
          <stop offset={0.994} stopColor="#0072EC" />
        </linearGradient>
        <linearGradient
          id="f"
          x1={1118.67}
          x2={1079.37}
          y1={897.443}
          y2={604.045}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.073} stopColor="#007FF9" />
          <stop offset={1} stopColor="#007CF6" />
        </linearGradient>
        <linearGradient
          id="g"
          x1={948.487}
          x2={1548.46}
          y1={435.378}
          y2={1712.05}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.073} stopColor="#007FF9" />
          <stop offset={1} stopColor="#0082FB" />
        </linearGradient>
        <linearGradient
          id="h"
          x1={810.919}
          x2={1534.45}
          y1={1734.88}
          y2={731.248}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.28} stopColor="#007FF8" />
          <stop offset={0.914} stopColor="#0082FB" />
        </linearGradient>
        <linearGradient
          id="i"
          x1={1788.89}
          x2={3396.01}
          y1={466.392}
          y2={6397.2}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#0082FB" />
          <stop offset={1} stopColor="#0081FA" />
        </linearGradient>
        <linearGradient
          id="j"
          x1={1452}
          x2={509.127}
          y1={269.626}
          y2={2181.27}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.062} stopColor="#0081FA" />
          <stop offset={1} stopColor="#0080F9" />
        </linearGradient>
        <linearGradient
          id="k"
          x1={802.399}
          x2={1563.17}
          y1={1683.5}
          y2={1163.72}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#027AF3" />
          <stop offset={1} stopColor="#0080F9" />
        </linearGradient>
        <linearGradient
          id="l"
          x1={606.893}
          x2={1994.42}
          y1={1235.65}
          y2={1235.65}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#0377EF" />
          <stop offset={0.999} stopColor="#0279F1" />
        </linearGradient>
        <linearGradient
          id="m"
          x1={1018.45}
          x2={1799.66}
          y1={1054.97}
          y2={1516.15}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.002} stopColor="#0471E9" />
          <stop offset={1} stopColor="#0377EF" />
        </linearGradient>
        <linearGradient
          id="n"
          x1={1482.75}
          x2={4034.55}
          y1={1304.7}
          y2={4522.9}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.277} stopColor="#0867DF" />
          <stop offset={1} stopColor="#0471E9" />
        </linearGradient>
        <clipPath id="meta">
          <path fill="#fff" d="M0 0h192v192H0z" />
        </clipPath>
      </defs>
    </svg>
  )
}
