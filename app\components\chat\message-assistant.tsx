import {
  Message,
  MessageAction,
  MessageActions,
  MessageContent,
} from "@/components/prompt-kit/message";
import { useUserPreferences } from "@/lib/user-preference-store/provider";
import { cn } from "@/lib/utils";
import type { Message as MessageAISDK, ToolInvocationUIPart } from "@ai-sdk/react";
import { ArrowClockwise, Check, Copy } from "@phosphor-icons/react";
import { useCallback, useRef } from "react";
import { getSources } from "./get-sources";
import { ProductSuggestions } from "./product-suggestions";
import { QuoteButton } from "./quote-button";
import { Reasoning } from "./reasoning";
import { SearchImages } from "./search-images";
import { SourcesList } from "./sources-list";
import { ToolInvocation } from "./tool-invocation";
import { useAssistantMessageSelection } from "./useAssistantMessageSelection";

type MessageAssistantProps = {
  children: string;
  isLast?: boolean;
  hasScrollAnchor?: boolean;
  copied?: boolean;
  copyToClipboard?: () => void;
  onReload?: () => void;
  parts?: MessageAISDK["parts"];
  status?: "streaming" | "ready" | "submitted" | "error";
  className?: string;
  messageId: string;
  onQuote?: (text: string, messageId: string) => void;
};

export function MessageAssistant({
  children,
  isLast,
  hasScrollAnchor,
  copied,
  copyToClipboard,
  onReload,
  parts,
  status,
  className,
  messageId,
  onQuote,
}: MessageAssistantProps) {
  const { preferences } = useUserPreferences();
  const sources = getSources(parts);

  // टाइप गार्ड लगाकर केवल tool-invocation वाले पार्ट्स लें
  const toolInvocationParts: ToolInvocationUIPart[] =
    parts?.filter(
      (part): part is ToolInvocationUIPart =>
        part.type === "tool-invocation" && !!part.toolInvocation
    ) ?? [];

  const reasoningParts = parts?.find((part) => part.type === "reasoning");

  // सर्च इमेज रिज़ल्ट्स फिल्टर करें
  const searchImageResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "imageSearch" &&
        Array.isArray(part.toolInvocation.result?.content) &&
        part.toolInvocation.result.content[0]?.type === "images"
    )
    .flatMap((part) => part.toolInvocation.result.content?.results ?? []);

  // प्रोडक्ट सजेशन रिज़ल्ट्स फिल्टर करें
  const productSuggestionResults = toolInvocationParts
    .filter(
      (part) =>
        part.toolInvocation.state === "result" &&
        part.toolInvocation.toolName === "suggest_products" &&
        part.toolInvocation.result?.results
    )
    .flatMap((part) => part.toolInvocation.result.results ?? []);

  const contentNullOrEmpty = children === null || children === "";

  const isQuoteEnabled = !preferences.multiModelEnabled;
  const messageRef = useRef<HTMLDivElement>(null);
  const { selectionInfo, clearSelection } = useAssistantMessageSelection(
    messageRef,
    isQuoteEnabled
  );

  const handleQuoteBtnClick = useCallback(() => {
    if (selectionInfo && onQuote) {
      onQuote(selectionInfo.text, selectionInfo.messageId);
      clearSelection();
    }
  }, [selectionInfo, onQuote, clearSelection]);

  const isLastStreaming = status === "streaming" && isLast;

  return (
    <Message
      className={cn(
        "group flex w-full max-w-3xl flex-1 items-start gap-4 px-6 pb-2",
        hasScrollAnchor && "min-h-scroll-anchor",
        className
      )}
    >
      <div
        ref={messageRef}
        className={cn("relative flex min-w-full flex-col gap-2", isLast && "pb-8")}
        {...(isQuoteEnabled && { "data-message-id": messageId })}
      >
        {/* Reasoning दिखाएं */}
        {reasoningParts?.reasoning && (
          <Reasoning
            reasoning={reasoningParts.reasoning}
            isStreaming={status === "streaming"}
          />
        )}

        {/* Tool Invocations दिखाएं */}
        {toolInvocationParts.length > 0 && preferences.showToolInvocations && (
          <ToolInvocation toolInvocations={toolInvocationParts} />
        )}

        {/* Search Images */}
        {searchImageResults.length > 0 && <SearchImages results={searchImageResults} />}

        {/* Product Suggestions */}
        {productSuggestionResults.length > 0 && <ProductSuggestions results={productSuggestionResults} />}

        {/* मेसेज कंटेंट */}
        {!contentNullOrEmpty && (
          <MessageContent
            className={cn(
              "prose dark:prose-invert relative min-w-full bg-transparent p-0",
              "prose-h1:scroll-m-20 prose-h1:text-2xl prose-h1:font-semibold prose-h2:mt-8 prose-h2:scroll-m-20 prose-h2:text-xl prose-h2:mb-3 prose-h2:font-medium prose-h3:scroll-m-20 prose-h3:text-base prose-h3:font-medium prose-h4:scroll-m-20 prose-h5:scroll-m-20 prose-h6:scroll-m-20 prose-strong:font-medium prose-table:block prose-table:overflow-y-auto"
            )}
            markdown={true}
          >
            {children}
          </MessageContent>
        )}

        {/* Sources */}
        {sources && sources.length > 0 && <SourcesList sources={sources} />}

        {/* Message Actions */}
        {!isLastStreaming && !contentNullOrEmpty && (
          <MessageActions className="-ml-2 flex gap-0 opacity-0 transition-opacity group-hover:opacity-100">
            <MessageAction tooltip={copied ? "Copied!" : "Copy text"} side="bottom">
              <button
                className="hover:bg-accent/60 text-muted-foreground hover:text-foreground flex size-7.5 items-center justify-center rounded-full bg-transparent transition"
                aria-label="Copy text"
                onClick={copyToClipboard}
                type="button"
              >
                {copied ? <Check className="size-4" /> : <Copy className="size-4" />}
              </button>
            </MessageAction>

            {isLast && (
              <MessageAction tooltip="Regenerate" side="bottom" delayDuration={0}>
                <button
                  className="hover:bg-accent/60 text-muted-foreground hover:text-foreground flex size-7.5 items-center justify-center rounded-full bg-transparent transition"
                  aria-label="Regenerate"
                  onClick={onReload}
                  type="button"
                >
                  <ArrowClockwise className="size-4" />
                </button>
              </MessageAction>
            )}
          </MessageActions>
        )}

        {/* Quote Button */}
        {isQuoteEnabled && selectionInfo && selectionInfo.messageId && (
          <QuoteButton
            mousePosition={selectionInfo.position}
            onQuote={handleQuoteBtnClick}
            messageContainerRef={messageRef}
            onDismiss={clearSelection}
          />
        )}
      </div>
    </Message>
  );
}
