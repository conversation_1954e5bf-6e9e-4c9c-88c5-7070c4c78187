// @/lib/assistant-store/utils (renamed path for consistency)
import { FREE_MODELS_IDS } from "@/lib/config" // Updated constant name if applicable
import { AssistantConfig } from "@/lib/models/types"

/**
 * Utility function to filter and sort assistants based on favorites, search, and visibility
 * @param assistants - All available assistants
 * @param favoriteAssistants - Array of favorite assistant IDs
 * @param searchQuery - Search query to filter by assistant name
 * @param isAssistantHidden - Function to check if an assistant is hidden
 * @returns Filtered and sorted assistants
 */
export function filterAndSortAssistants(
  assistants: AssistantConfig[],
  favoriteAssistants: string[],
  searchQuery: string,
  isAssistantHidden: (assistantId: string) => boolean
): AssistantConfig[] {
  return assistants
    .filter((assistant) => !isAssistantHidden(assistant.id))
    .filter((assistant) => {
      // If user has favorite assistants, only show favorites
      if (favoriteAssistants && favoriteAssistants.length > 0) {
        return favoriteAssistants.includes(assistant.id)
      }
      // If no favorites, show all assistants
      return true
    })
    .filter((assistant) =>
      assistant.name.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      // If user has favorite assistants, maintain their order
      if (favoriteAssistants && favoriteAssistants.length > 0) {
        const aIndex = favoriteAssistants.indexOf(a.id)
        const bIndex = favoriteAssistants.indexOf(b.id)
        return aIndex - bIndex
      }

      // Fallback to original sorting (free assistants first)
      const aIsFree = FREE_MODELS_IDS.includes(a.id)
      const bIsFree = FREE_MODELS_IDS.includes(b.id)
      return aIsFree === bIsFree ? 0 : aIsFree ? -1 : 1
    })
}
