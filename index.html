<!DOCTYPE html>
<html lang="hi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Voice Screen Hindi TTS & STT</title>
  <style>
    body {
      background: #FBFAF5;
      color: #111;
      font-family: Arial, sans-serif;
      margin: 0;
      height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    #circle {
      width: 160px;
      height: 160px;
      border-radius: 50%;
      background-color: black;
      box-shadow: 0 0 250px 50px rgba(0, 208, 255, 0.25);
      margin-bottom: 32px;
      transition: background-color 0.3s ease;
    }
    #circle.listening {
      background-color: red;
    }
    #message {
      font-size: 1rem;
      text-align: center;
      margin-bottom: 48px;
    }
    button {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      font-size: 24px;
      margin: 0 12px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
      transition: background-color 0.2s ease;
    }
    button:hover {
      background-color: #ddd;
    }
    .container {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  </style>
</head>
<body>

  <div id="circle"></div>
  <div id="message">नमस्ते, आप कैसे हैं?</div>
  <div class="container">
    <button id="micBtn" title="Start/Stop Listening">🎤</button>
    <button id="closeBtn" title="Close">✖</button>
  </div>

  <script>
    const micBtn = document.getElementById("micBtn");
    const closeBtn = document.getElementById("closeBtn");
    const circle = document.getElementById("circle");
    const messageDiv = document.getElementById("message");

    let listening = false;
    let recognition;
    let selectedVoice = null;
    let voiceReady = false;

    function loadVoices() {
      const voices = speechSynthesis.getVoices();
      if (voices.length) {
        selectedVoice = voices.find(
          v => v.lang === "hi-IN" && v.name.includes("Google हिन्दी")
        ) || null;
        voiceReady = selectedVoice !== null;
        if (voiceReady) {
          speakTextHindi(messageDiv.textContent);
        }
      }
    }

    // Load voices and retry if not ready
    function setupVoices() {
      loadVoices();
      if (!voiceReady) {
        speechSynthesis.onvoiceschanged = () => {
          loadVoices();
        };
      }
    }

    function speakTextHindi(text) {
      if (!voiceReady) {
        console.warn("Hindi voice not ready yet.");
        return;
      }
      speechSynthesis.cancel();
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = "hi-IN";
      utterance.voice = selectedVoice;
      utterance.pitch = 1;
      utterance.rate = 1;
      speechSynthesis.speak(utterance);
    }

    function toggleListening() {
      if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
        alert("SpeechRecognition not supported in this browser.");
        return;
      }

      if (!recognition) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();
        recognition.lang = "hi-IN";
        recognition.interimResults = false;
        recognition.continuous = false;

        recognition.onresult = (event) => {
          const transcript = event.results[0].transcript;
          messageDiv.textContent = "आपने कहा: " + transcript;
          speakTextHindi(messageDiv.textContent);
        };

        recognition.onerror = (err) => {
          console.error(err);
          listening = false;
          updateUI();
        }

        recognition.onend = () => {
          listening = false;
          updateUI();
        }
      }

      if (!listening) {
        recognition.start();
        listening = true;
        updateUI();
      } else {
        recognition.stop();
        listening = false;
        updateUI();
      }
    }

    function updateUI() {
      if (listening) {
        circle.classList.add("listening");
        micBtn.style.color = "red";
      } else {
        circle.classList.remove("listening");
        micBtn.style.color = "black";
      }
    }

    micBtn.addEventListener("click", () => {
      if (!voiceReady) {
        alert("Hindi voice loading, कृपया कुछ क्षण प्रतीक्षा करें और पुनः प्रयास करें।");
        return;
      }
      toggleListening();
    });

    closeBtn.addEventListener("click", () => {
      window.close(); // या आपकी अपनी क्लोज़ मेथड यहाँ डालें
    });

    // Initialize voices on load
    setupVoices();

    // Also try to load voices every 500ms until ready, max 10 tries
    let retryCount = 0;
    let retryInterval = setInterval(() => {
      if (voiceReady || retryCount > 10) {
        clearInterval(retryInterval);
      } else {
        loadVoices();
        retryCount++;
      }
    }, 500);

  </script>
</body>
</html>
