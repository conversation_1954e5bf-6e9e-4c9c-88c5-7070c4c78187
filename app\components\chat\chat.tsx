"use client";

import { ChatInput } from "@/app/components/chat-input/chat-input";
import { Conversation } from "@/app/components/chat/conversation";
import { useAssistant } from "@/app/components/chat/use-model";
import { useChatDraft } from "@/app/hooks/use-chat-draft";
import { useChats } from "@/lib/chat-store/chats/provider";
import { useMessages } from "@/lib/chat-store/messages/provider";
import { useChatSession } from "@/lib/chat-store/session/provider";
import { SYSTEM_PROMPT_DEFAULT } from "@/lib/config";
import { useUserPreferences } from "@/lib/user-preference-store/provider";
import { useUser } from "@/lib/user-store/provider";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "motion/react";
import dynamic from "next/dynamic";
import { redirect } from "next/navigation";
import { useCallback, useMemo, useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useChatCore } from "./use-chat-core";
import { useChatOperations } from "./use-chat-operations";
import { useFileUpload } from "./use-file-upload";
import VoiceScreen from "../voice/Screen";
import Heading from '@/app/components/chat/heading';

const FeedbackWidget = dynamic(
  () => import("./feedback-widget").then((mod) => mod.FeedbackWidget),
  { ssr: false }
);

const DialogAuth = dynamic(
  () => import("./dialog-auth").then((mod) => mod.DialogAuth),
  { ssr: false }
);

export function Chat() {
  const { chatId } = useChatSession();
  const searchParams = useSearchParams();
  const {
    createNewChat,
    getChatById,
    updateChatModel,
    bumpChat,
    isLoading: isChatsLoading,
  } = useChats();

  const currentChat = useMemo(
    () => (chatId ? getChatById(chatId) : null),
    [chatId, getChatById]
  );

  const { messages: initialMessages, cacheAndAddMessage } = useMessages();
  const { user } = useUser();
  const { preferences } = useUserPreferences();
  const { draftValue, clearDraft } = useChatDraft(chatId);

  // Handle URL query parameter for direct message input
  const [hasProcessedUrlQuery, setHasProcessedUrlQuery] = useState(false);

  // File upload
  const {
    files,
    setFiles,
    handleFileUploads,
    createOptimisticAttachments,
    cleanupOptimisticAttachments,
    handleFileUpload,
    handleFileRemove,
  } = useFileUpload();

  // Model selection
  const { selectedModel, handleModelChange } = useAssistant({
    currentChat: currentChat || null,
    user,
    updateChatModel,
    chatId,
  });

  const [hasDialogAuth, setHasDialogAuth] = useState(false);
  const isAuthenticated = useMemo(() => !!user?.id, [user?.id]);
  const systemPrompt = useMemo(
    () => user?.system_prompt || SYSTEM_PROMPT_DEFAULT,
    [user?.system_prompt]
  );

  const [quotedText, setQuotedText] = useState<{ text: string; messageId: string }>();
  const handleQuotedSelected = useCallback(
    (text: string, messageId: string) => {
      setQuotedText({ text, messageId });
    },
    []
  );

  const { checkLimitsAndNotify, ensureChatExists, handleDelete, handleEdit } =
    useChatOperations({
      isAuthenticated,
      chatId,
      messages: initialMessages,
      selectedModel,
      systemPrompt,
      createNewChat,
      setHasDialogAuth,
      setMessages: () => { },
      setInput: () => { },
    });

  const {
    messages,
    input,
    status,
    stop,
    hasSentFirstMessageRef,
    isSubmitting,
    enableSearch,
    setEnableSearch,
    submit,
    handleSuggestion,
    handleReload,
    handleInputChange,
  } = useChatCore({
    initialMessages,
    draftValue,
    cacheAndAddMessage,
    chatId,
    user,
    files,
    createOptimisticAttachments,
    setFiles,
    checkLimitsAndNotify,
    cleanupOptimisticAttachments,
    ensureChatExists,
    handleFileUploads,
    selectedModel,
    clearDraft,
    bumpChat,
  });

  // Handle URL query parameter for direct message input
  useEffect(() => {
    if (!hasProcessedUrlQuery && searchParams && handleSuggestion) {
      const queryMessage = searchParams.get('q');
      console.log('URL query parameter found:', queryMessage);

      if (queryMessage && queryMessage.trim()) {
        console.log('Auto-sending message directly:', queryMessage);
        setHasProcessedUrlQuery(true);

        // Use handleSuggestion directly - it's more reliable for auto-sending
        setTimeout(() => {
          console.log('Sending message via handleSuggestion:', queryMessage);
          handleSuggestion(queryMessage);
        }, 1000); // 1 second delay
      }
    }
  }, [searchParams, hasProcessedUrlQuery, handleSuggestion]);

  const conversationProps = useMemo(
    () => ({
      messages,
      status,
      onDelete: handleDelete,
      onEdit: handleEdit,
      onReload: handleReload,
      onQuote: handleQuotedSelected,
    }),
    [messages, status, handleDelete, handleEdit, handleReload, handleQuotedSelected]
  );

  // ✅ Voice popup state
  const [showVoicePopup, setShowVoicePopup] = useState(false);
  const handleVoiceClick = useCallback(() => {
    setShowVoicePopup(true);
  }, []);

  const chatInputProps = useMemo(
    () => ({
      value: input,
      onSuggestion: handleSuggestion,
      onValueChange: handleInputChange,
      onSend: submit,
      isSubmitting,
      files,
      onFileUpload: handleFileUpload,
      onFileRemove: handleFileRemove,
      hasSuggestions:
        preferences.promptSuggestions && !chatId && messages.length === 0,
      onSelectModel: handleModelChange,
      selectedModel,
      isUserAuthenticated: isAuthenticated,
      stop,
      status,
      setEnableSearch,
      enableSearch,
      quotedText,
      onVoiceClick: handleVoiceClick, // Pass mic click function
    }),
    [
      input,
      handleSuggestion,
      handleInputChange,
      submit,
      isSubmitting,
      files,
      handleFileUpload,
      handleFileRemove,
      preferences.promptSuggestions,
      chatId,
      messages.length,
      handleModelChange,
      selectedModel,
      isAuthenticated,
      stop,
      status,
      setEnableSearch,
      enableSearch,
      quotedText,
      handleVoiceClick,
    ]
  );

  if (
    chatId &&
    !isChatsLoading &&
    !currentChat &&
    !isSubmitting &&
    status === "ready" &&
    messages.length === 0 &&
    !hasSentFirstMessageRef.current
  ) {
    return redirect("/");
  }

  const showOnboarding = !chatId && messages.length === 0;

  return (
    <div className={cn(
      "@container/main relative flex h-full flex-col items-center justify-end md:justify-center"
    )}>
      <DialogAuth open={hasDialogAuth} setOpen={setHasDialogAuth} />

      <AnimatePresence initial={false} mode="popLayout">
        {showOnboarding ? (
          <motion.div
            key="onboarding"
            className="absolute bottom-[60%] mx-auto max-w-[50rem] md:relative md:bottom-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <Heading />
          </motion.div>
        ) : (
          <Conversation key="conversation" {...conversationProps} />
        )}
      </AnimatePresence>

      <motion.div className="relative inset-x-0 bottom-0 z-50 mx-auto w-full max-w-3xl">
        <ChatInput {...chatInputProps} />
      </motion.div>

      <AnimatePresence>
        {showVoicePopup && (
          <motion.div
            key="voice-screen"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="z-50 fixed inset-0"
          >
            <VoiceScreen onClose={() => setShowVoicePopup(false)} />
          </motion.div>
        )}
      </AnimatePresence>



      <FeedbackWidget authUserId={user?.id} />
    </div>
  );
}
