import { useEffect, useState } from "react"

interface User {
  id: string
  display_name: string
  email: string
  profile_image?: string
}

// JWT decode helper
function decodeJwt(token: string): any | null {
  try {
    const parts = token.split(".")
    if (parts.length !== 3) return null
    const payload = parts[1]
    const decoded = JSON.parse(atob(payload))
    return decoded
  } catch (e) {
    return null
  }
}

export function useFetchUserFromToken(token?: string) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!token) {
      setUser(null)
      setError(null)
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    const decoded = decodeJwt(token)
    const userId = decoded?.userId

    if (!userId) {
      setError("Unable to decode token")
      setLoading(false)
      setUser(null)
      return
    }

    fetch(`/api/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,  // send the token to backend for auth
      },
    })
      .then(res => {
        if (!res.ok) throw new Error("Failed to fetch user")
        return res.json()
      })
      .then(data => setUser(data))
      .catch(err => setError(err.message))
      .finally(() => setLoading(false))
  }, [token])

  return { user, loading, error }
}
