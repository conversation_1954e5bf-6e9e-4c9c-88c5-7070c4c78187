// nityashaModels.ts

import type { ModelConfig } from "../types"
import { openproviders } from "@/lib/openproviders"

const nityashaModels: ModelConfig[] = [
  {
    id: "sonar",
    name: "<PERSON><PERSON><PERSON>a Sonar",
    provider: "<PERSON><PERSON><PERSON><PERSON>",
    providerId: "<PERSON>tyash<PERSON>",
    modelFamily: "Sonar",
    baseProviderId: "Nityasha",
    description:
      "Sonar is a fast, affordable QA model with source control and citation support—ideal for lightweight, real-time integrations.",
    tags: ["fast", "simple", "affordable", "QA"],
    contextWindow: 127072,
    inputCost: 1,
    outputCost: 1,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://Nityasha.ai",
    apiDocs: "https://docs.Nityasha.ai/home",
    modelPage: "https://docs.Nityasha.ai/models/models/sonar",
    releasedAt: "2025-01-27",
    icon: "Nityasha",
    apiSdk: (apiKey?: string) => openproviders("sonar", undefined, apiKey),
  },
  {
    id: "sonar-reasoning",
    name: "Nityasha Sonar Reasoning",
    provider: "Nityasha",
    providerId: "Nityasha",
    modelFamily: "Sonar",
    baseProviderId: "Nityasha",
    description:
      "An enhanced version of Sonar optimized for deeper reasoning and more complex tasks, while retaining fast response times.",
    tags: ["reasoning", "fast", "QA", "affordable"],
    contextWindow: 127072,
    inputCost: 1,
    outputCost: 5,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://docs.Nityasha.ai/home",
    apiDocs: "https://docs.Nityasha.ai/api-reference/chat-completions-post",
    modelPage: "https://docs.Nityasha.ai/models/models/sonar-reasoning",
    releasedAt: "2025-01-29",
    icon: "Nityasha",
    apiSdk: (apiKey?: string) => openproviders("sonar-reasoning", undefined, apiKey),
  }
]

export { nityashaModels }
