import { openproviders } from "@/lib/openproviders"
import { ModelConfig } from "../types"

const openaiModels: ModelConfig[] = [
  {
    id: "gpt-3.5-turbo",
    name: "GPT-3.5 Turbo",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "GPT-3.5",
    baseProviderId: "openai",
    description: "Legacy GPT model for cheaper chat and non-chat tasks",
    tags: ["fast", "cheap", "chat"],
    contextWindow: 16385,
    inputCost: 0.5,
    outputCost: 1.5,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    openSource: false,
    speed: "Fast",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/gpt-3.5-turbo",
    modelPage: "https://platform.openai.com/docs/models/gpt-3.5-turbo",
    icon: "openai",
    apiSdk: (apiKey?: string) =>
      openproviders("gpt-3.5-turbo", undefined, apiKey),
  },
  {
    id: "gpt-4-turbo",
    name: "GPT-4 Turbo",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "GPT-4",
    baseProviderId: "openai",
    description: "An older high-intelligence GPT model",
    tags: ["vision", "tools", "large-context"],
    contextWindow: 128000,
    inputCost: 10.0,
    outputCost: 30.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    openSource: false,
    speed: "Medium",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/gpt-4-turbo",
    modelPage: "https://platform.openai.com/docs/models/gpt-4-turbo",
    icon: "openai",
    apiSdk: (apiKey?: string) =>
      openproviders("gpt-4-turbo", undefined, apiKey),
  },
  {
    id: "gpt-4.1",
    name: "GPT-4.1",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "GPT-4",
    baseProviderId: "openai",
    description: "Flagship GPT model for complex tasks",
    tags: ["reasoning", "tools", "large-context"],
    contextWindow: 1047576,
    inputCost: 2.0,
    outputCost: 8.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    openSource: false,
    speed: "Medium",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/gpt-4.1",
    modelPage: "https://platform.openai.com/docs/models/gpt-4.1",
    icon: "openai",
    apiSdk: (apiKey?: string) => openproviders("gpt-4.1", undefined, apiKey),
  },
  {
    id: "gpt-4.1-mini",
    name: "GPT-4.1 Mini",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "GPT-4",
    baseProviderId: "openai",
    description: "Balanced for intelligence, speed, and cost",
    tags: ["fast", "efficient", "tools"],
    contextWindow: 1047576,
    inputCost: 0.4,
    outputCost: 1.6,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    openSource: false,
    speed: "Fast",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/gpt-4.1-mini",
    modelPage: "https://platform.openai.com/docs/models/gpt-4.1-mini",
    icon: "openai",
    apiSdk: (apiKey?: string) =>
      openproviders("gpt-4.1-mini", undefined, apiKey),
  },
  {
    id: "gpt-4.1-nano",
    name: "GPT-4.1 Nano",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "GPT-4",
    baseProviderId: "openai",
    description: "Fastest, most cost-effective GPT-4.1 model",
    tags: ["fast", "cheap", "efficient"],
    contextWindow: 1047576,
    inputCost: 0.1,
    outputCost: 0.4,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    openSource: false,
    speed: "Fast",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/gpt-4.1-mini",
    modelPage: "https://platform.openai.com/docs/models/gpt-4.1-mini",
    icon: "openai",
    apiSdk: (apiKey?: string) =>
      openproviders("gpt-4.1-nano", undefined, apiKey),
  },
  {
    id: "gpt-4.5-preview",
    name: "GPT-4.5 Preview",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "GPT-4",
    baseProviderId: "openai",
    description: "Largest and most capable GPT model",
    tags: ["preview", "advanced", "reasoning"],
    contextWindow: 128000,
    inputCost: 75.0,
    outputCost: 150.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    openSource: false,
    speed: "Fast",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/gpt-4.5-preview",
    modelPage: "https://platform.openai.com/docs/models/gpt-4.5-preview",
    icon: "openai",
    apiSdk: (apiKey?: string) =>
      openproviders("gpt-4.5-preview", undefined, apiKey),
  },
  {
    id: "gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "GPT-4o",
    baseProviderId: "openai",
    description: "Fast, intelligent, flexible GPT model",
    tags: ["vision", "tools", "multimodal", "flagship"],
    contextWindow: 128000,
    inputCost: 2.5,
    outputCost: 10.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    openSource: false,
    speed: "Medium",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/gpt-4o",
    modelPage: "https://platform.openai.com/docs/models/gpt-4o",
    icon: "openai",
    apiSdk: (apiKey?: string) => openproviders("gpt-4o", undefined, apiKey),
  },
  {
    id: "gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "GPT-4o",
    baseProviderId: "openai",
    description: "Fast, affordable small model for focused tasks",
    tags: ["fast", "cheap", "vision", "multimodal"],
    contextWindow: 128000,
    inputCost: 0.15,
    outputCost: 0.6,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    openSource: false,
    speed: "Fast",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/gpt-4o-mini",
    modelPage: "https://platform.openai.com/docs/models/gpt-4o-mini",
    icon: "openai",
    apiSdk: (apiKey?: string) =>
      openproviders("gpt-4o-mini", undefined, apiKey),
  },
  {
    id: "o1",
    name: "o1",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "o1",
    baseProviderId: "openai",
    description: "Previous full o-series reasoning model",
    tags: ["reasoning", "complex", "research"],
    contextWindow: 200000,
    inputCost: 15.0,
    outputCost: 60.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    openSource: false,
    speed: "Slow",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/o1",
    modelPage: "https://platform.openai.com/docs/models/o1",
    releasedAt: "2024-12-20",
    icon: "openai",
    apiSdk: (apiKey?: string) => openproviders("o1", undefined, apiKey),
  },
  {
    id: "o3-mini",
    name: "o3-mini",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "o3",
    baseProviderId: "openai",
    description: "A small model alternative to o3",
    tags: ["reasoning", "efficient", "balanced"],
    contextWindow: 200000,
    inputCost: 1.1,
    outputCost: 4.4,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    openSource: false,
    speed: "Medium",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/o3-mini",
    modelPage: "https://platform.openai.com/docs/models/o3-mini",
    releasedAt: "2024-12-20",
    icon: "openai",
    apiSdk: (apiKey?: string) => openproviders("o3-mini", undefined, apiKey),
  },
  {
    id: "o1-mini",
    name: "o1-mini",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "o1",
    baseProviderId: "openai",
    description: "A small model alternative to o1",
    tags: ["reasoning", "high-compute", "performance"],
    contextWindow: 128000,
    inputCost: 1.1,
    outputCost: 4.4,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    openSource: false,
    speed: "Slow",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/o1-mini",
    modelPage: "https://platform.openai.com/docs/models/o1-mini",
    releasedAt: "2024-12-20",
    icon: "openai",
    apiSdk: (apiKey?: string) => openproviders("o1-mini", undefined, apiKey),
  },
  {
    id: "o3",
    name: "o3",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "o3",
    baseProviderId: "openai",
    description: "Our most powerful reasoning model",
    tags: ["reasoning", "high-compute", "performance"],
    contextWindow: 200000,
    inputCost: 10,
    outputCost: 40,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    openSource: false,
    speed: "Slow",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/o3",
    modelPage: "https://platform.openai.com/docs/models/o3",
    releasedAt: "2024-12-20",
    icon: "openai",
    apiSdk: (apiKey?: string) => openproviders("o3-mini", undefined, apiKey),
  },
  {
    id: "o4-mini",
    name: "o4-mini",
    provider: "OpenAI",
    providerId: "openai",
    modelFamily: "o4",
    baseProviderId: "openai",
    description: "Faster, more affordable reasoning model",
    tags: ["reasoning", "next-gen", "preview"],
    contextWindow: 200000,
    inputCost: 1.1,
    outputCost: 4.4,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    openSource: false,
    speed: "Medium",
    website: "https://openai.com",
    apiDocs: "https://platform.openai.com/docs/models/o4-mini",
    modelPage: "https://platform.openai.com/docs/models/o4-mini",
    icon: "openai",
    apiSdk: (apiKey?: string) => openproviders("o4-mini", undefined, apiKey),
  },
]

export { openaiModels }
