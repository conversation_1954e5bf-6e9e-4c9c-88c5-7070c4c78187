import { createHash, randomBytes } from "crypto";
import { cookies } from "next/headers";

const CSRF_SECRET = process.env.CSRF_SECRET;

if (!CSRF_SECRET) {
  throw new Error("CSRF_SECRET is not set in environment variables.");
}

export function generateCsrfToken(): string {
  const raw = randomBytes(32).toString("hex");
  const token = createHash("sha256")
    .update(`${raw}${CSRF_SECRET}`)  // Fixed: No backticks needed
    .digest("hex");
  return `${raw}:${token}`;
}

export function validateCsrfToken(fullToken: string): boolean {
  const [raw, token] = fullToken.split(":");
  if (!raw || !token) return false;
  const expected = createHash("sha256")
    .update(`${raw}${CSRF_SECRET}`)  // Fixed: No backticks needed
    .digest("hex");
  return expected === token;
}

export async function setCsrfCookie() {
  try {
    const cookieStore = await cookies();
    const token = generateCsrfToken();
    cookieStore.set("csrf_token", token, {
      httpOnly: false,  // Client-side accessible
      secure: process.env.NODE_ENV === "production",  // Secure in prod only
      path: "/",
      sameSite: "strict",  // Added for better security
      maxAge: 60 * 60 * 24,  // 1 day expiration (adjust as needed)
    });
    return token;
  } catch (error) {
    console.error("Failed to set CSRF cookie:", error);
    throw error;
  }
}
