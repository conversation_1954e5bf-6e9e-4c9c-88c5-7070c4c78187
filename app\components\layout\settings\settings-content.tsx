"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DrawerClose } from "@/components/ui/drawer"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { isSupabaseEnabled } from "@/lib/supabase/config"
import { cn, isDev } from "@/lib/utils"
import {
  GearSixIcon,
  PaintBrushIcon,
  XIcon,
  ListChecksIcon, // Tasks icon
  BellIcon, // Reminder icon
  ArchiveBoxIcon, // Memory icon (using ArchiveBox as a placeholder for memory-related icon; adjust as needed)
} from "@phosphor-icons/react"
import { ByokSection } from "./apikeys/byok-section"
import { InteractionPreferences } from "./appearance/interaction-preferences"
import { LayoutSettings } from "./appearance/layout-settings"
import { ThemeSelection } from "./appearance/theme-selection"
import { ConnectionsPlaceholder } from "./connections/connections-placeholder"
import { DeveloperTools } from "./connections/developer-tools"
import { OllamaSection } from "./connections/ollama-section"
import { AccountManagement } from "./general/account-management"
import { UserProfile } from "./general/user-profile"
import { ModelsSettings } from "./models/models-settings"
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { TaskPreferences } from "./appearance/task-preferences"
import { ReminderPreferences } from "./appearance/reminder-preferences"
import { MemoryPreferences } from "./appearance/memory-preferences" // Assuming this component exists or needs to be created; adjust path as needed

type SettingsContentProps = {
  isDrawer?: boolean
}

// Updated TabType with "reminder" and "memory"
type TabType =
  | "general"
  | "appearance"
  | "models"
  | "connections"
  | "tasks"
  | "reminder"
  | "memory"

export function SettingsContent({ isDrawer = false }: SettingsContentProps) {
  const [activeTab, setActiveTab] = useState<TabType>("general")

  return (
    <div
      className={cn(
        "flex w-full flex-col overflow-y-auto",
        isDrawer ? "p-0 pb-16" : "py-0"
      )}
    >
      {isDrawer && (
        <div className="border-border mb-2 flex items-center justify-between border-b px-4 pb-2">
          <h2 className="text-lg font-medium">Settings</h2>
          <DrawerClose asChild>
            <Button variant="ghost" size="icon">
              <XIcon className="size-4" />
            </Button>
          </DrawerClose>
        </div>
      )}

      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as TabType)}
        className={cn("flex w-full flex-row", isDrawer ? "" : "flex min-h-[400px]")}
      >
        {isDrawer ? (
          // Mobile version - tabs on top
          <div className="w-full items-start justify-start overflow-hidden py-4">
            <div>
              <TabsList className="mb-4 flex w-full min-w-0 flex-nowrap items-center justify-start overflow-x-auto bg-transparent px-0">
                <TabsTrigger
                  value="general"
                  className="ml-6 flex shrink-0 items-center gap-2"
                >
                  <GearSixIcon className="size-4" />
                  <span>General</span>
                </TabsTrigger>
                <TabsTrigger
                  value="appearance"
                  className="flex shrink-0 items-center gap-2"
                >
                  <PaintBrushIcon className="size-4" />
                  <span>Appearance</span>
                </TabsTrigger>
                <TabsTrigger
                  value="tasks"
                  className="flex shrink-0 items-center gap-2"
                >
                  <ListChecksIcon className="size-4" />
                  <span>Tasks</span>
                </TabsTrigger>
                <TabsTrigger
                  value="reminder"
                  className="flex shrink-0 items-center gap-2"
                >
                  <BellIcon className="size-4" />
                  <span>Reminder</span>
                </TabsTrigger>
                <TabsTrigger
                  value="memory"
                  className="flex shrink-0 items-center gap-2"
                >
                  <ArchiveBoxIcon className="size-4" />
                  <span>Memory</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Mobile tabs content */}
            <TabsContent value="general" className="space-y-6 px-6">
              <UserProfile />
              {isSupabaseEnabled && (
                <>
                  <AccountManagement />
                </>
              )}
            </TabsContent>

            <TabsContent value="appearance" className="space-y-6 px-6">
              <ThemeSelection />
              <LayoutSettings />
              <InteractionPreferences />
            </TabsContent>

            <TabsContent value="apikeys" className="px-6">
              <ByokSection />
            </TabsContent>

            <TabsContent value="models" className="px-6">
              <ModelsSettings />
            </TabsContent>

            <TabsContent value="connections" className="space-y-6 px-6">
              {!isDev && <ConnectionsPlaceholder />}
              {isDev && <OllamaSection />}
              {isDev && <DeveloperTools />}
            </TabsContent>

            <TabsContent value="tasks" className="space-y-6 px-6">
              <TaskPreferences />
            </TabsContent>

            <TabsContent value="reminder" className="space-y-6 px-6">
              <ReminderPreferences />
            </TabsContent>

            <TabsContent value="memory" className="space-y-6 px-6">
              <MemoryPreferences />
            </TabsContent>
          </div>
        ) : (
          // Desktop version - tabs on left
          <>
            <TabsList className="block w-48 rounded-none bg-transparent px-3 pt-4">
              <div className="flex w-full flex-col gap-1">
                <TabsTrigger
                  value="general"
                  className="w-full justify-start rounded-md px-3 py-2 text-left"
                >
                  <div className="flex items-center gap-2">
                    <GearSixIcon className="size-4" />
                    <span>General</span>
                  </div>
                </TabsTrigger>

                <TabsTrigger
                  value="appearance"
                  className="w-full justify-start rounded-md px-3 py-2 text-left"
                >
                  <div className="flex items-center gap-2">
                    <PaintBrushIcon className="size-4" />
                    <span>Appearance</span>
                  </div>
                </TabsTrigger>

                <TabsTrigger
                  value="tasks"
                  className="w-full justify-start rounded-md px-3 py-2 text-left"
                >
                  <div className="flex items-center gap-2">
                    <ListChecksIcon className="size-4" />
                    <span>Tasks</span>
                  </div>
                </TabsTrigger>

                <TabsTrigger
                  value="reminder"
                  className="w-full justify-start rounded-md px-3 py-2 text-left"
                >
                  <div className="flex items-center gap-2">
                    <BellIcon className="size-4" />
                    <span>Reminder</span>
                  </div>
                </TabsTrigger>

                <TabsTrigger
                  value="memory"
                  className="w-full justify-start rounded-md px-3 py-2 text-left"
                >
                  <div className="flex items-center gap-2">
                    <ArchiveBoxIcon className="size-4" />
                    <span>Memory</span>
                  </div>
                </TabsTrigger>
              </div>
            </TabsList>

            {/* Desktop tabs content */}
            <div className="flex-1 overflow-auto px-6 pt-4">
              <TabsContent value="general" className="mt-0 space-y-6">
                <UserProfile />
                {isSupabaseEnabled && (
                  <>
                    <AccountManagement />
                  </>
                )}
              </TabsContent>

              <TabsContent value="appearance" className="mt-0 space-y-6">
                <ThemeSelection />
                <LayoutSettings />
                <InteractionPreferences />
              </TabsContent>

              <TabsContent value="apikeys" className="mt-0 space-y-6">
                <ByokSection />
              </TabsContent>

              <TabsContent value="models" className="mt-0 space-y-6">
                <ModelsSettings />
              </TabsContent>

              <TabsContent value="connections" className="mt-0 space-y-6">
                {!isDev && <ConnectionsPlaceholder />}
                {isDev && <OllamaSection />}
                {isDev && <DeveloperTools />}
              </TabsContent>

              <TabsContent value="tasks" className="mt-0 space-y-6">
                <TaskPreferences />
              </TabsContent>

              <TabsContent value="reminder" className="mt-0 space-y-6">
                <ReminderPreferences />
              </TabsContent>

              <TabsContent value="memory" className="mt-0 space-y-6">
                <MemoryPreferences />
              </TabsContent>
            </div>
          </>
        )}
      </Tabs>
    </div>
  )
}
