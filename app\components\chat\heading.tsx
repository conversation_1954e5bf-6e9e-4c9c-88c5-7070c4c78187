import React from 'react'
import { useUser } from "@/lib/user-store/provider"

export default function Heading() {
  const { user } = useUser()

  // Extract first name if display_name is available
  const firstName = user?.display_name
    ? user.display_name.split(' ')[0]
    : 'Guest' // fallback to Guest

  return (
    <h1 className="mb-6 text-3xl font-medium tracking-tight capitalize">
      What&apos;s on your mind, {firstName}?
    </h1>
  )
}