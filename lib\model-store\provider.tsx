"use client"

import { fetchClient } from "@/lib/fetch"
import { ModelConfig } from "@/lib/models/types" // Note: Rename this to AssistantConfig if customizing types further
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react"

type UserKeyStatus = {
  openrouter: boolean
  openai: boolean
  mistral: boolean
  google: boolean
  perplexity: boolean
  xai: boolean
  anthropic: boolean
  [key: string]: boolean // Allow for additional providers
}

type AssistantContextType = {
  assistants: ModelConfig[] // Use AssistantConfig if renamed
  userKeyStatus: UserKeyStatus
  favoriteAssistants: string[]
  isLoading: boolean
  refreshAssistants: () => Promise<void>
  refreshUserKeyStatus: () => Promise<void>
  refreshFavoriteAssistants: () => Promise<void>
  refreshFavoriteAssistantsSilent: () => Promise<void>
  refreshAll: () => Promise<void>
}

const AssistantContext = createContext<AssistantContextType | undefined>(undefined)

export function AssistantP<PERSON>ider({ children }: { children: React.ReactNode }) {
  const [assistants, setAssistants] = useState<ModelConfig[]>([])
  const [userKeyStatus, setUserKeyStatus] = useState<UserKeyStatus>({
    openrouter: false,
    openai: false,
    mistral: false,
    google: false,
    perplexity: false,
    xai: false,
    anthropic: false,
  })
  const [favoriteAssistants, setFavoriteAssistants] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const fetchAssistants = useCallback(async () => {
    try {
      const response = await fetchClient("/api/models") // Update endpoint if needed for assistants
      if (response.ok) {
        const data = await response.json()
        setAssistants(data.models || []) // Adjust key if API response changes
      }
    } catch (error) {
      console.error("Failed to fetch assistants:", error)
    }
  }, [])

  const fetchUserKeyStatus = useCallback(async () => {
    try {
      const response = await fetchClient("/api/user-key-status")
      if (response.ok) {
        const data = await response.json()
        setUserKeyStatus(data)
      }
    } catch (error) {
      console.error("Failed to fetch user key status:", error)
      // Set default values on error
      setUserKeyStatus({
        openrouter: false,
        openai: false,
        mistral: false,
        google: false,
        perplexity: false,
        xai: false,
        anthropic: false,
      })
    }
  }, [])

  const fetchFavoriteAssistants = useCallback(async () => {
    try {
      const response = await fetchClient(
        "/api/user-preferences/favorite-models" // Update endpoint if needed for assistants
      )
      if (response.ok) {
        const data = await response.json()
        setFavoriteAssistants(data.favorite_models || []) // Adjust key if API response changes
      }
    } catch (error) {
      console.error("Failed to fetch favorite assistants:", error)
      setFavoriteAssistants([])
    }
  }, [])

  const refreshAssistants = useCallback(async () => {
    setIsLoading(true)
    try {
      await fetchAssistants()
    } finally {
      setIsLoading(false)
    }
  }, [fetchAssistants])

  const refreshUserKeyStatus = useCallback(async () => {
    setIsLoading(true)
    try {
      await fetchUserKeyStatus()
    } finally {
      setIsLoading(false)
    }
  }, [fetchUserKeyStatus])

  const refreshFavoriteAssistants = useCallback(async () => {
    setIsLoading(true)
    try {
      await fetchFavoriteAssistants()
    } finally {
      setIsLoading(false)
    }
  }, [fetchFavoriteAssistants])

  const refreshFavoriteAssistantsSilent = useCallback(async () => {
    try {
      await fetchFavoriteAssistants()
    } catch (error) {
      console.error(
        "❌ AssistantProvider: Failed to silently refresh favorite assistants:",
        error
      )
    }
  }, [fetchFavoriteAssistants])

  const refreshAll = useCallback(async () => {
    setIsLoading(true)
    try {
      await Promise.all([
        fetchAssistants(),
        fetchUserKeyStatus(),
        fetchFavoriteAssistants(),
      ])
    } finally {
      setIsLoading(false)
    }
  }, [fetchAssistants, fetchUserKeyStatus, fetchFavoriteAssistants])

  // Initial data fetch
  useEffect(() => {
    refreshAll()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []) // Only run once on mount

  return (
    <AssistantContext.Provider
      value={{
        assistants,
        userKeyStatus,
        favoriteAssistants,
        isLoading,
        refreshAssistants,
        refreshUserKeyStatus,
        refreshFavoriteAssistants,
        refreshFavoriteAssistantsSilent,
        refreshAll,
      }}
    >
      {children}
    </AssistantContext.Provider>
  )
}

// Custom hook to use the assistant context
export function useAssistant() {
  const context = useContext(AssistantContext)
  if (context === undefined) {
    throw new Error("useAssistant must be used within an AssistantProvider")
  }
  return context
}
