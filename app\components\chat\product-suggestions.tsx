import Image from "next/image"
import { useState } from "react"
import { addUTM, getFavicon, getSiteName } from "./utils"

type ProductResult = {
  name: string
  price?: string
  link: string
  image?: string
  description?: string
}

export function ProductSuggestions({ results }: { results: ProductResult[] }) {
  const [hiddenIndexes, setHiddenIndexes] = useState<Set<number>>(new Set())

  const handleError = (index: number) => {
    setHiddenIndexes((prev) => new Set(prev).add(index))
  }

  if (!results?.length) return null

  return (
    <div className="my-4 space-y-4">
      <h3 className="text-lg font-medium text-foreground">Product Suggestions</h3>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {results.map((product, i) => {
          const favicon = getFavicon(product.link)
          return hiddenIndexes.has(i) ? null : (
            <a
              key={i}
              href={addUTM(product.link)}
              target="_blank"
              rel="noopener noreferrer"
              className="group/product relative block overflow-hidden rounded-xl border border-border bg-card hover:bg-accent/50 transition-colors"
            >
              {/* Product Image */}
              {product.image && (
                <div className="relative h-48 w-full overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    onError={() => handleError(i)}
                    onLoad={(e) => e.currentTarget.classList.remove("opacity-0")}
                    className="object-cover opacity-0 transition-opacity duration-150 ease-out"
                  />
                </div>
              )}
              
              {/* Product Info */}
              <div className="p-4">
                <div className="flex items-start justify-between gap-2 mb-2">
                  <h4 className="font-medium text-foreground line-clamp-2 text-sm">
                    {product.name}
                  </h4>
                  {product.price && (
                    <span className="text-primary font-semibold text-sm whitespace-nowrap">
                      {product.price}
                    </span>
                  )}
                </div>
                
                {product.description && (
                  <p className="text-muted-foreground text-xs line-clamp-2 mb-3">
                    {product.description}
                  </p>
                )}
                
                {/* Source Info */}
                <div className="flex items-center gap-1">
                  {favicon && (
                    <img
                      src={favicon}
                      alt="favicon"
                      width={16}
                      height={16}
                      className="rounded-full"
                    />
                  )}
                  <span className="text-muted-foreground line-clamp-1 text-xs">
                    {getSiteName(product.link)}
                  </span>
                </div>
              </div>
            </a>
          )
        })}
      </div>
    </div>
  )
}
