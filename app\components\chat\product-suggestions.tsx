import Image from "next/image"
import { useState } from "react"
import { addUTM, getFavicon, getSiteName } from "./utils"

type ProductResult = {
  name: string
  price?: string
  originalPrice?: string
  discount?: string
  link: string
  image?: string
  description?: string
  platform?: string
  rating?: string
  affiliateLink?: string
}

// Helper function to add Flipkart affiliate parameters
const addFlipkartAffiliate = (url: string): string => {
  if (url.includes('flipkart.com')) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}affid=6e80384ac6764f619e4ccab4819d3bf1&affExtParam1=product_suggestion`;
  }
  return url;
};

// Helper function to detect platform
const getPlatform = (url: string): string => {
  if (url.includes('flipkart.com')) return 'Flipkart';
  if (url.includes('amazon.')) return 'Amazon';
  if (url.includes('myntra.com')) return 'Myntra';
  if (url.includes('ajio.com')) return 'Ajio';
  if (url.includes('nykaa.com')) return 'Nykaa';
  return 'Other';
};

// Helper function to format price properly
const formatPrice = (price: string | number | object | undefined): string => {
  console.log('formatPrice called with:', price, 'type:', typeof price);

  if (!price) {
    console.log('Price is falsy, returning empty string');
    return '';
  }

  // If it's already a string and looks like a formatted price, return as is
  if (typeof price === 'string') {
    console.log('Price is string:', price);
    // If it already has currency symbol, return as is
    if (price.includes('₹') || price.includes('Rs') || price.includes('$')) {
      console.log('Price already has currency symbol, returning as is');
      return price;
    }
    // If it's just numbers, format it
    const numericPrice = parseFloat(price.replace(/[^\d.]/g, ''));
    if (!isNaN(numericPrice)) {
      const formatted = `₹${numericPrice.toLocaleString('en-IN')}`;
      console.log('Formatted numeric string to:', formatted);
      return formatted;
    }
    console.log('Returning string as is:', price);
    return price;
  }

  // If it's a number, format it with ₹
  if (typeof price === 'number') {
    const formatted = `₹${price.toLocaleString('en-IN')}`;
    console.log('Formatted number to:', formatted);
    return formatted;
  }

  // If it's an object, try to extract meaningful value
  if (typeof price === 'object' && price !== null) {
    console.log('Price is object:', JSON.stringify(price));
    const priceObj = price as any;
    if (priceObj.value) {
      console.log('Found value property:', priceObj.value);
      return formatPrice(priceObj.value);
    }
    if (priceObj.amount) {
      console.log('Found amount property:', priceObj.amount);
      return formatPrice(priceObj.amount);
    }
    if (priceObj.price) {
      console.log('Found price property:', priceObj.price);
      return formatPrice(priceObj.price);
    }
    // If object has no recognizable properties, return empty
    console.log('Object has no recognizable price properties, returning empty');
    return '';
  }

  // Fallback: convert to string
  const fallback = String(price);
  console.log('Fallback string conversion:', fallback);
  return fallback;
};

export function ProductSuggestions({ results }: { results: ProductResult[] }) {
  const [hiddenIndexes, setHiddenIndexes] = useState<Set<number>>(new Set())

  const handleError = (index: number) => {
    setHiddenIndexes((prev) => new Set(prev).add(index))
  }

  // Debug logging to see what we're receiving
  console.log('ProductSuggestions received results:', results);
  results.forEach((product, index) => {
    console.log(`Product ${index}:`, {
      name: product.name,
      price: product.price,
      priceType: typeof product.price,
      originalPrice: product.originalPrice,
      originalPriceType: typeof product.originalPrice,
    });
  });

  if (!results?.length) return null

  return (
    <div className="my-4 space-y-4">
      <h3 className="text-lg font-medium text-foreground">Product Suggestions</h3>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {results.map((product, i) => {
          const favicon = getFavicon(product.link)
          const platform = getPlatform(product.link)
          const finalLink = addFlipkartAffiliate(product.affiliateLink || product.link)

          return hiddenIndexes.has(i) ? null : (
            <a
              key={i}
              href={addUTM(finalLink)}
              target="_blank"
              rel="noopener noreferrer"
              className="group/product relative block overflow-hidden rounded-xl border border-border bg-card hover:bg-accent/50 transition-colors"
            >
              {/* Product Image */}
              {product.image && (
                <div className="relative h-48 w-full overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    onError={() => handleError(i)}
                    onLoad={(e) => e.currentTarget.classList.remove("opacity-0")}
                    className="object-cover opacity-0 transition-opacity duration-150 ease-out"
                  />
                </div>
              )}
              
              {/* Product Info */}
              <div className="p-4">
                <div className="flex items-start justify-between gap-2 mb-2">
                  <h4 className="font-medium text-foreground line-clamp-2 text-sm">
                    {product.name}
                  </h4>
                  <div className="flex flex-col items-end gap-1">
                    {product.price && (
                      <span className="text-primary font-bold text-sm whitespace-nowrap">
                        {formatPrice(product.price)}
                      </span>
                    )}
                    {product.originalPrice && product.originalPrice !== product.price && (
                      <span className="text-muted-foreground line-through text-xs">
                        {formatPrice(product.originalPrice)}
                      </span>
                    )}
                    {product.discount && (
                      <span className="text-green-600 font-medium text-xs">
                        {product.discount}
                      </span>
                    )}
                  </div>
                </div>

                {/* Platform Badge */}
                {platform && (
                  <div className="mb-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      platform === 'Flipkart' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      platform === 'Amazon' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                    }`}>
                      {platform}
                      {platform === 'Flipkart' && ' 🛒'}
                      {platform === 'Amazon' && ' 📦'}
                    </span>
                  </div>
                )}

                {product.rating && (
                  <div className="mb-2">
                    <span className="text-yellow-500 text-xs">
                      ⭐ {product.rating}
                    </span>
                  </div>
                )}

                {product.description && (
                  <p className="text-muted-foreground text-xs line-clamp-2 mb-3">
                    {product.description}
                  </p>
                )}

                {/* Source Info */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {favicon && (
                      <img
                        src={favicon}
                        alt="favicon"
                        width={16}
                        height={16}
                        className="rounded-full"
                      />
                    )}
                    <span className="text-muted-foreground line-clamp-1 text-xs">
                      {getSiteName(product.link)}
                    </span>
                  </div>
                  {platform === 'Flipkart' && (
                    <span className="text-xs text-green-600 font-medium">
                      Affiliate Link
                    </span>
                  )}
                </div>
              </div>
            </a>
          )
        })}
      </div>
    </div>
  )
}
