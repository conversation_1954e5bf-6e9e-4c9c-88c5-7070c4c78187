import Image from "next/image"
import { useState } from "react"
import { addUTM, getFavicon, getSiteName } from "./utils"

type ProductResult = {
  name: string
  price?: string
  originalPrice?: string
  discount?: string
  link: string
  image?: string
  description?: string
  platform?: string
  rating?: string
  affiliateLink?: string
}

// Helper function to add Flipkart affiliate parameters
const addFlipkartAffiliate = (url: string): string => {
  if (url.includes('flipkart.com')) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}affid=6e80384ac6764f619e4ccab4819d3bf1&affExtParam1=product_suggestion`;
  }
  return url;
};

// Helper function to detect platform
const getPlatform = (url: string): string => {
  if (url.includes('flipkart.com')) return 'Flipkart';
  if (url.includes('amazon.')) return 'Amazon';
  if (url.includes('myntra.com')) return 'Myntra';
  if (url.includes('ajio.com')) return 'Ajio';
  if (url.includes('nykaa.com')) return 'Nykaa';
  return 'Other';
};

// Helper function to format price properly
const formatPrice = (price: string | number | object | undefined): string => {
  if (!price || price === 'undefined' || price === 'null') {
    return '';
  }

  // If it's already a string and looks like a formatted price, return as is
  if (typeof price === 'string') {
    // Handle special cases
    if (price === 'Price not available' || price === 'undefined' || price === 'null') {
      return '';
    }

    // If it already has currency symbol, return as is
    if (price.includes('₹') || price.includes('Rs') || price.includes('$')) {
      return price;
    }

    // Try to extract numbers from the string
    const numericMatch = price.match(/[\d,]+\.?\d*/);
    if (numericMatch) {
      const cleanNumber = numericMatch[0].replace(/,/g, '');
      const numericPrice = parseFloat(cleanNumber);
      if (!isNaN(numericPrice) && numericPrice > 0) {
        return `₹${numericPrice.toLocaleString('en-IN')}`;
      }
    }

    return '';
  }

  // If it's a number, format it with ₹
  if (typeof price === 'number' && !isNaN(price) && price > 0) {
    return `₹${price.toLocaleString('en-IN')}`;
  }

  // If it's an object, try to extract meaningful value
  if (typeof price === 'object' && price !== null) {
    const priceObj = price as any;

    // Try different possible properties
    const possibleKeys = ['value', 'amount', 'price', 'cost', 'selling_price', 'mrp'];
    for (const key of possibleKeys) {
      if (priceObj[key] !== undefined && priceObj[key] !== null) {
        return formatPrice(priceObj[key]);
      }
    }

    return '';
  }

  // Fallback: return empty for anything else
  return '';
};

export function ProductSuggestions({ results }: { results: ProductResult[] }) {
  const [hiddenIndexes, setHiddenIndexes] = useState<Set<number>>(new Set())

  const handleError = (index: number) => {
    setHiddenIndexes((prev) => new Set(prev).add(index))
  }

  if (!results?.length) return null

  return (
    <div className="my-4 space-y-4">
      <h3 className="text-lg font-medium text-foreground">Product Suggestions</h3>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {results.map((product, i) => {
          const favicon = getFavicon(product.link)
          const platform = getPlatform(product.link)
          const finalLink = addFlipkartAffiliate(product.affiliateLink || product.link)

          return hiddenIndexes.has(i) ? null : (
            <a
              key={i}
              href={addUTM(finalLink)}
              target="_blank"
              rel="noopener noreferrer"
              className="group/product relative block overflow-hidden rounded-xl border border-border bg-card hover:bg-accent/50 transition-colors"
            >
              {/* Product Image */}
              {product.image && (
                <div className="relative h-48 w-full overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    onError={() => handleError(i)}
                    onLoad={(e) => e.currentTarget.classList.remove("opacity-0")}
                    className="object-cover opacity-0 transition-opacity duration-150 ease-out"
                  />
                </div>
              )}
              
              {/* Product Info */}
              <div className="p-4">
                <div className="flex items-start justify-between gap-2 mb-2">
                  <h4 className="font-medium text-foreground line-clamp-2 text-sm">
                    {product.name}
                  </h4>
                  <div className="flex flex-col items-end gap-1">
                    {product.price && formatPrice(product.price) && (
                      <span className="text-primary font-bold text-sm whitespace-nowrap">
                        {formatPrice(product.price)}
                      </span>
                    )}
                    {product.originalPrice && formatPrice(product.originalPrice) && product.originalPrice !== product.price && (
                      <span className="text-muted-foreground line-through text-xs">
                        {formatPrice(product.originalPrice)}
                      </span>
                    )}
                    {product.discount && (
                      <span className="text-green-600 font-medium text-xs">
                        {product.discount}
                      </span>
                    )}
                  </div>
                </div>

                {/* Platform Badge */}
                {platform && (
                  <div className="mb-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      platform === 'Flipkart' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      platform === 'Amazon' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                    }`}>
                      {platform}
                      {platform === 'Flipkart' && ' 🛒'}
                      {platform === 'Amazon' && ' 📦'}
                    </span>
                  </div>
                )}

                {product.rating && (
                  <div className="mb-2">
                    <span className="text-yellow-500 text-xs">
                      ⭐ {product.rating}
                    </span>
                  </div>
                )}

                {product.description && (
                  <p className="text-muted-foreground text-xs line-clamp-2 mb-3">
                    {product.description}
                  </p>
                )}

                {/* Source Info */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {favicon && (
                      <img
                        src={favicon}
                        alt="favicon"
                        width={16}
                        height={16}
                        className="rounded-full"
                      />
                    )}
                    <span className="text-muted-foreground line-clamp-1 text-xs">
                      {getSiteName(product.link)}
                    </span>
                  </div>
                  {platform === 'Flipkart' && (
                    <span className="text-xs text-green-600 font-medium">
                      Affiliate Link
                    </span>
                  )}
                </div>
              </div>
            </a>
          )
        })}
      </div>
    </div>
  )
}
