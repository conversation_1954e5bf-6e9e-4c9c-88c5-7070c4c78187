import { openproviders } from "@/lib/openproviders"
import { ModelConfig } from "../types"

const claudeModels: ModelConfig[] = [
  {
    id: "claude-3-5-haiku-latest",
    name: "<PERSON> 3.5 Haiku",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 3.5",
    baseProviderId: "claude",
    description:
      "Lightweight Claude model optimized for fast, low-cost output.",
    tags: ["fast", "cheap", "lightweight"],
    contextWindow: 200000,
    inputCost: 0.25,
    outputCost: 1.25,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    modelPage: "https://www.anthropic.com/news/claude-3-5-haiku",
    icon: "claude",
    apiSdk: (apiKey?: string) =>
      openproviders("claude-3-5-haiku-latest", undefined, apiKey),
  },
  {
    id: "claude-3-5-sonnet-latest",
    name: "<PERSON> 3.5 Sonnet",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 3.5",
    baseProviderId: "claude",
    description:
      "Balanced Claude model for general-purpose chat and reasoning.",
    tags: ["balanced", "reasoning", "flagship"],
    contextWindow: 200000,
    inputCost: 3.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    modelPage: "https://www.anthropic.com/news/claude-3-5-sonnet",
    releasedAt: "2024-06-20",
    icon: "claude",
    apiSdk: (apiKey?: string) =>
      openproviders("claude-3-5-sonnet-latest", undefined, apiKey),
  },
  {
    id: "claude-3-7-sonnet-20250219",
    name: "Claude 3.7 Sonnet",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 3.7",
    baseProviderId: "claude",
    description: "Upgraded Claude with improved performance and speed.",
    tags: ["upgraded", "performance", "reasoning"],
    contextWindow: 200000,
    inputCost: 3.5,
    outputCost: 17.5,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    icon: "claude",
    apiSdk: (apiKey?: string) =>
      openproviders("claude-3-7-sonnet-20250219", undefined, apiKey),
  },
  {
    id: "claude-3-7-sonnet-rea",
    name: "Claude 3.7 Sonnet (REA)",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 3.7",
    baseProviderId: "claude",
    description: "Realtime Enhanced Assistant version of Claude 3.7 Sonnet.",
    tags: ["realtime", "enhanced", "assistant", "fast"],
    contextWindow: 200000,
    inputCost: 4.0,
    outputCost: 20.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    reasoning: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    icon: "claude",
    apiSdk: (apiKey?: string) =>
      openproviders("claude-3-7-sonnet-20250219", undefined, apiKey),
  },
  {
    id: "claude-3-haiku-20240307",
    name: "Claude 3 Haiku",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 3",
    baseProviderId: "claude",
    description: "Fast Claude model for lightweight tasks.",
    tags: ["fast", "lightweight", "cheap"],
    contextWindow: 200000,
    inputCost: 0.25,
    outputCost: 1.25,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    modelPage: "https://www.anthropic.com/news/claude-3-family",
    releasedAt: "2024-03-04",
    icon: "claude",
    apiSdk: (apiKey?: string) =>
      openproviders("claude-3-haiku-20240307", undefined, apiKey),
  },
  {
    id: "claude-3-opus-latest",
    name: "Claude 3 Opus",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 3",
    baseProviderId: "claude",
    description: "Anthropic's most powerful Claude 3 model.",
    tags: ["powerful", "flagship", "reasoning", "complex"],
    contextWindow: 200000,
    inputCost: 15.0,
    outputCost: 75.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    modelPage: "https://www.anthropic.com/news/claude-3-family",
    releasedAt: "2024-03-04",
    icon: "claude",
    apiSdk: (apiKey?: string) =>
      openproviders("claude-3-opus-latest", undefined, apiKey),
  },
  {
    id: "claude-3-sonnet-20240229",
    name: "Claude 3 Sonnet",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 3",
    baseProviderId: "claude",
    description:
      "Mid-tier Claude model for balance between cost and intelligence.",
    tags: ["balanced", "mid-tier", "cost-effective"],
    contextWindow: 200000,
    inputCost: 3.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    modelPage: "https://www.anthropic.com/news/claude-3-family",
    releasedAt: "2024-03-04",
    icon: "claude",
    apiSdk: (apiKey?: string) =>
      openproviders("claude-3-sonnet-20240229", undefined, apiKey),
  },
  {
    id: "claude-4-opus",
    name: "Claude 4 Opus",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 4",
    baseProviderId: "claude",
    description:
      "Preview of the upcoming Claude 4 Opus model with enhanced reasoning.",
    tags: ["preview", "next-gen", "reasoning", "advanced"],
    contextWindow: 500000,
    inputCost: 25.0,
    outputCost: 125.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    icon: "claude",
  },
  {
    id: "claude-4-sonnet",
    name: "Claude 4 Sonnet",
    provider: "Anthropic",
    providerId: "anthropic",
    modelFamily: "Claude 4",
    baseProviderId: "claude",
    description: "Sonnet variant of the Claude 4 model family.",
    tags: ["next-gen", "balanced", "preview"],
    contextWindow: 500000,
    inputCost: 5.0,
    outputCost: 25.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://www.anthropic.com",
    apiDocs: "https://docs.anthropic.com",
    icon: "claude",
  },
]

export { claudeModels }
