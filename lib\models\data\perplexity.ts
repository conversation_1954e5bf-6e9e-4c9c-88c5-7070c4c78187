import { openproviders } from "@/lib/openproviders"
import { ModelConfig } from "../types"

const perplexityModels: ModelConfig[] = [
  {
    id: "sonar",
    name: "Perplexity Sonar",
    provider: "Perplexity",
    providerId: "perplexity",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "Sonar is a fast, affordable QA model with source control and citation support—ideal for lightweight, real-time integrations.",
    tags: ["fast", "simple", "affordable", "QA"],
    contextWindow: 127072,
    inputCost: 1,
    outputCost: 1,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://perplexity.ai",
    apiDocs: "https://docs.perplexity.ai/home",
    modelPage: "https://docs.perplexity.ai/models/models/sonar",
    releasedAt: "2025-01-27",
    icon: "perplexity",
    apiSdk: (apiKey?: string) => openproviders("sonar", undefined, apiKey),
  },
  {
    id: "sonar-reasoning",
    name: "Perplexity Sonar Reasoning",
    provider: "Perplexity",
    providerId: "perplexity",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "An enhanced version of Sonar optimized for deeper reasoning and more complex tasks, while retaining fast response times.",
    tags: ["reasoning", "fast", "QA", "affordable"],
    contextWindow: 127072,
    inputCost: 1,
    outputCost: 5,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://docs.perplexity.ai/home",
    apiDocs: "https://docs.perplexity.ai/api-reference/chat-completions-post",
    modelPage: "https://docs.perplexity.ai/models/models/sonar-reasoning",
    releasedAt: "2025-01-29",
    icon: "perplexity",
    apiSdk: (apiKey?: string) =>
      openproviders("sonar-reasoning", undefined, apiKey),
  },
  {
    id: "sonar-reasoning-pro",
    name: "Perplexity Sonar Reasoning Pro",
    provider: "Perplexity",
    providerId: "perplexity",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "Perplexity's most advanced Sonar model with pro-level reasoning, accuracy, and context handling—ideal for complex tasks.",
    tags: ["reasoning", "pro", "advanced", "QA", "research"],
    contextWindow: 127072,
    inputCost: 2,
    outputCost: 8,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://docs.perplexity.ai/home",
    apiDocs: "https://docs.perplexity.ai/api-reference/chat-completions-post",
    modelPage: "https://docs.perplexity.ai/models/models/sonar-reasoning-pro",
    releasedAt: "2025-07-25",
    icon: "perplexity",
    apiSdk: (apiKey?: string) =>
      openproviders("sonar-reasoning-pro", undefined, apiKey),
  },
  {
    id: "sonar-pro",
    name: "Perplexity Sonar Pro",
    provider: "Perplexity",
    providerId: "perplexity",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "A high-performance version of Sonar optimized for speed and accuracy across general tasks, with solid reasoning capabilities.",
    tags: ["fast", "accurate", "QA", "general-purpose"],
    contextWindow: 200000,
    inputCost: 3,
    outputCost: 15,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Fast",
    intelligence: "High",
    website: "https://docs.perplexity.ai/home",
    apiDocs: "https://docs.perplexity.ai/api-reference/chat-completions-post",
    modelPage: "https://docs.perplexity.ai/models/models/sonar-pro",
    releasedAt: "2025-03-27",
    icon: "perplexity",
    apiSdk: (apiKey?: string) => openproviders("sonar-pro", undefined, apiKey),
  },
  {
    id: "sonar-deep-research",
    name: "Perplexity Sonar Deep Research",
    provider: "Perplexity",
    providerId: "perplexity",
    modelFamily: "Sonar",
    baseProviderId: "perplexity",
    description:
      "Perplexity's most powerful model for deep research, long-context understanding, and advanced reasoning tasks.",
    tags: ["deep research", "advanced", "long-context", "reasoning", "QA"],
    contextWindow: 128000,
    inputCost: 2,
    outputCost: 8,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://docs.perplexity.ai/home",
    apiDocs: "https://docs.perplexity.ai/api-reference/chat-completions-post",
    modelPage: "https://docs.perplexity.ai/models/model-cards",
    releasedAt: "2025-03-07",
    icon: "perplexity",
    apiSdk: (apiKey?: string) =>
      openproviders("sonar-deep-research", undefined, apiKey),
  },
]

export { perplexityModels }
