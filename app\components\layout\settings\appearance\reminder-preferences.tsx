"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input"; // Assume you have an Input component from Shadcn/UI
import { createBrowserClient } from "@supabase/ssr"; // Use browser client for client components
import { useUser } from "@/lib/user-store/provider";

// Update Reminder type to match your Supabase table schema
type Reminder = {
  id: number;
  text: string;
  timing: string | null; // Time column for notification timing (e.g., ISO 8601 format)
  done: boolean;
  user_id: string; // Adjust type based on your schema (e.g., string for UUID)
};

// Create the Supabase client once (browser client is synchronous and safe for client-side)
const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export function ReminderPreferences() {
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [newText, setNewText] = useState(""); // State for new reminder text
  const [newTiming, setNewTiming] = useState(""); // State for new reminder timing (datetime-local input)
  const { user } = useUser();

  // Derive userId from the user object (assuming user has an 'id' property)
  const userId = user?.id; // Adjust based on your user object's structure

  const fetchReminders = useCallback(async () => {
    if (!userId) return; // Prevent fetch if no userId

    const { data, error } = await supabase
      .from("reminders")
      .select("*")
      .eq("user_id", userId); // Filter by user_id if reminders are user-specific

    if (error) {
      console.error("Error fetching reminders:", error);
      return;
    }

    setReminders((data as Reminder[]) || []);
  }, [userId]);

  useEffect(() => {
    fetchReminders();
  }, [fetchReminders]);

  const toggleDone = async (id: number) => {
    const reminderToUpdate = reminders.find((reminder) => reminder.id === id);
    if (!reminderToUpdate || !userId) return;

    const { error } = await supabase
      .from("reminders")
      .update({ done: !reminderToUpdate.done })
      .eq("id", id)
      .eq("user_id", userId); // Ensure only user's reminder is updated

    if (error) {
      console.error("Error updating reminder:", error);
      return;
    }

    // Refresh local state
    fetchReminders();
  };

  // Function to add a new reminder with text and timing
  const addReminder = async () => {
    if (!newText || !userId) return;

    const { error } = await supabase.from("reminders").insert({
      text: newText,
      timing: newTiming || null, // Use null if no timing is set
      done: false,
      user_id: userId,
    });

    if (error) {
      console.error("Error adding reminder:", error);
      return;
    }

    // Clear inputs and refresh list
    setNewText("");
    setNewTiming("");
    fetchReminders();
  };

  const upcomingReminders = reminders.filter((r) => !r.done);
  const completedReminders = reminders.filter((r) => r.done);

  return (
    <div className="space-y-8">
      {/* Form to add new reminder with text and timing */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Add New Reminder</h3>
        <Input
          type="text"
          placeholder="Reminder text"
          value={newText}
          onChange={(e) => setNewText(e.target.value)}
        />
        <Input
          type="datetime-local"
          placeholder="Set due date and time"
          value={newTiming}
          onChange={(e) => setNewTiming(e.target.value)}
        />
        <Button onClick={addReminder}>Add Reminder</Button>
      </div>

      <hr className="border-border" />

      <h3 className="text-lg font-semibold">Your Upcoming Reminders</h3>
      {upcomingReminders.length === 0 ? (
        <Card className="p-4 text-center text-muted-foreground shadow-none">
          No upcoming reminders.
        </Card>
      ) : (
        <div className="space-y-2 max-h-72 overflow-y-auto pr-1">
          {upcomingReminders.map(({ id, text, timing }) => (
            <Card
              key={id}
              className="flex items-center justify-between flex-row p-2 px-4 shadow-none hover:bg-muted rounded-lg transition"
            >
              <div className="flex flex-col">
                <span className="text-foreground">{text}</span>
                {timing && <span className="text-sm text-muted-foreground">Due: {new Date(timing).toLocaleString()}</span>}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => toggleDone(id)}
                aria-label="Mark as done"
                title="Mark as done"
              >
                ✓
              </Button>
            </Card>
          ))}
        </div>
      )}

      <hr className="border-border" />

      <h3 className="text-lg font-semibold">Completed Reminders</h3>
      {completedReminders.length === 0 ? (
        <Card className="p-4 text-center text-muted-foreground shadow-none">
          No completed reminders.
        </Card>
      ) : (
        <div className="space-y-2 max-h-72 overflow-y-auto pr-1">
          {completedReminders.map(({ id, text, timing }) => (
            <Card
              key={id}
              className="flex items-center justify-between flex-row p-2 px-4 hover:bg-muted rounded-lg transition shadow-none"
            >
              <div className="flex flex-col">
                <span className="text-muted-foreground line-through">{text}</span>
                {timing && <span className="text-sm text-muted-foreground">Due: {new Date(timing).toLocaleString()}</span>}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => toggleDone(id)}
                aria-label="Mark as not done"
                title="Mark as not done"
              >
                ↺
              </Button>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
