// /api/auth/login.ts
import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { Expo, ExpoPushTicket } from 'expo-server-sdk';

type UserRow = {
  id: number;
  username: string;
  pushtoken: string;
};

const pool = mysql.createPool({
  host: process.env.MYSQL_HOST!,
  user: process.env.MYSQL_USER!,
  password: process.env.MYSQL_PASSWORD!,
  database: process.env.MYSQL_DATABASE!,
  waitForConnections: true,
  connectionLimit: 10,
});

function generateOtp(length: number = 6): string {
  return Math.floor(Math.pow(10, length - 1) + Math.random() * 9 * Math.pow(10, length - 1)).toString();
}

export async function POST(request: NextRequest) {
  const { phoneNumber, title } = (await request.json()) as {
    phoneNumber: string;
    title?: string;
  };

  const [userRows] = await pool.execute<UserRow[]>(
    'SELECT id, username, pushtoken FROM users WHERE email = ?',
    [phoneNumber]
  );

  if (!Array.isArray(userRows) || userRows.length === 0) {
    return NextResponse.json({ error: 'User/token not found' }, { status: 404 });
  }

  const user = userRows[0];
  if (!user.pushtoken) {
    return NextResponse.json({ error: 'Push token missing for user' }, { status: 404 });
  }

  const expo = new Expo();
  if (!Expo.isExpoPushToken(user.pushtoken)) {
    return NextResponse.json({ error: 'Invalid Expo push token' }, { status: 400 });
  }

  // Generate OTP and save it to 'otps' table with 5 minutes expiry
  const otp = generateOtp(6);
  const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now

  await pool.execute(
    'INSERT INTO otps (phone, otp, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE otp = ?, expires_at = ?',
    [phoneNumber, otp, expiresAt, otp, expiresAt]
  );

  const messages = [
    {
      to: user.pushtoken,
      sound: 'default',
      title: title || 'Your OTP Code',
      body: `Your OTP is: ${otp}`,
      data: { otp },
    },
  ];

  try {
    const tickets: ExpoPushTicket[] = [];
    const chunks = expo.chunkPushNotifications(messages);
    for (const chunk of chunks) {
      const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
      tickets.push(...(ticketChunk as ExpoPushTicket[]));
    }
    return NextResponse.json({ success: true, tickets });
  } catch (error) {
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
}
