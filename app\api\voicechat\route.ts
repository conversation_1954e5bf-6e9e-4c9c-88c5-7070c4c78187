import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: NextRequest) {
  try {
    // इनकमिंग POST रिक्वेस्ट से बॉडी पढ़ें
    const body = await request.json();

    // वैकल्पिक वैलिडेशन (अगर message नहीं है तो एरर)
    if (!body.message) {
      return NextResponse.json({ error: 'message field is required' }, { status: 400 });
    }

    // एक्सटर्नल API पर POST रिक्वेस्ट भेजें (इनकमिंग बॉडी को फॉरवर्ड करें)
    const postResponse = await axios.post('https://nx.human.api.nityasha.com/chat', {
      message: body.message,  // इनकमिंग message यूज करें
      user_id: body.user_id || '213'  // user_id अगर हो तो यूज करें, नहीं तो डिफॉल्ट
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(postResponse.data)

    // POST से मिला रिस्पॉन्स
    const data = postResponse.data;

    // रिस्पॉन्स दिखाएं
    return NextResponse.json({ data });
  } catch (error) {
    // अगर एरर आए (जैसे नेटवर्क, CSP, या इनवैलिड JSON)
    return NextResponse.json({ error: error.message || 'API कॉल में समस्या आई' }, { status: 500 });
  }
}
