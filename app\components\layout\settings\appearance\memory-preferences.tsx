"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { createBrowserClient } from "@supabase/ssr" // Use browser client for client components
import { useUser } from "@/lib/user-store/provider"

// Update Memory type to match your Supabase table schema
type Memory = {
  id: number
  title: string
  content: string
  created_at: string // ISO format timestamp
  done: boolean
  user_id: string // Adjust type based on your schema (e.g., string for UUID)
}

// Create the Supabase client once (browser client is synchronous and safe for client-side)
const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export function MemoryPreferences() {
  const [memories, setMemories] = useState<Memory[]>([])
  const { user } = useUser()

  // Derive userId from the user object (assuming user has an 'id' property)
  const userId = user?.id // Adjust based on your user object's structure

  const fetchMemories = useCallback(async () => {
    if (!userId) return // Prevent fetch if no userId

    const { data, error } = await supabase
      .from("memories")
      .select("*")
      .eq("user_id", userId) // Filter by user_id if memories are user-specific

    if (error) {
      console.error("Error fetching memories:", error)
      return
    }

    setMemories((data as Memory[]) || [])
  }, [userId])

  useEffect(() => {
    fetchMemories()
  }, [fetchMemories])

  const toggleDone = async (id: number) => {
    const memoryToUpdate = memories.find((memory) => memory.id === id)
    if (!memoryToUpdate || !userId) return

    const { error } = await supabase
      .from("memories")
      .update({ done: !memoryToUpdate.done })
      .eq("id", id)
      .eq("user_id", userId) // Ensure only user's memory is updated

    if (error) {
      console.error("Error updating memory:", error)
      return
    }

    // Refresh local state
    fetchMemories()
  }

  const deleteMemory = async (id: number) => {
    if (!userId) return

    const { error } = await supabase
      .from("memories")
      .delete()
      .eq("id", id)
      .eq("user_id", userId) // Ensure only user's memory is deleted

    if (error) {
      console.error("Error deleting memory:", error)
      return
    }

    // Refresh local state
    fetchMemories()
  }

  return (
    <div className="space-y-8">
      <h3 className="text-lg font-semibold">Your Memories</h3>
      {memories.length === 0 ? (
        <Card className="p-4 text-center text-muted-foreground shadow-none">
          No memories found.
        </Card>
      ) : (
        <div className="space-y-2 max-h-72 overflow-y-auto pr-1">
          {memories.map(({ id, title, content, created_at, done }) => (
            <Card
              key={id}
              className="flex items-center justify-between flex-row p-2 px-4 shadow-none hover:bg-muted rounded-lg transition"
            >
              <div className="flex flex-col">
                <span className={`text-foreground ${done ? 'line-through text-muted-foreground' : ''}`}>{title}</span>
                <span className="text-sm text-muted-foreground">{content.slice(0, 100)}...</span>
                <span className="text-sm text-muted-foreground">Created: {new Date(created_at).toLocaleString()}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => toggleDone(id)}
                  aria-label={done ? "Mark as active" : "Mark as archived"}
                >
                  {done ? '↺' : '✓'}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => deleteMemory(id)}
                  aria-label="Delete memory"
                >
                  🗑️
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
