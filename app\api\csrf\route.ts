import { setCsrfCookie } from "@/lib/csrf";  // Import the utility
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const token = await setCsrfCookie();  // Use the utility function
    return NextResponse.json({ ok: true, token });  // Optionally return the token in response
  } catch (error) {
    console.error("CSRF API error:", error);
    return NextResponse.json({ error: "Failed to generate CSRF token" }, { status: 500 });
  }
}
