"use client"

import { PopoverContentAuth } from "@/app/components/chat-input/popover-content-auth"
import { useBreakpoint } from "@/app/hooks/use-breakpoint"
import { useKeyShortcut } from "@/app/hooks/use-key-shortcut"
import { Button } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>rigger,
} from "@/components/ui/drawer"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Popover, PopoverTrigger } from "@/components/ui/popover"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useAssistant } from "@/lib/model-store/provider" // Updated import path (adjust if needed)
import { filterAndSortAssistants } from "@/lib/model-store/utils" // Updated utils import (adjust if needed)
import { AssistantConfig } from "@/lib/models/types" // Updated types import (adjust if needed)
import { PROVIDERS } from "@/lib/providers"
import { useUserPreferences } from "@/lib/user-preference-store/provider"
import { cn } from "@/lib/utils"
import {
  CaretDownIcon,
  MagnifyingGlassIcon,
  StarIcon,
} from "@phosphor-icons/react"
import { useRef, useState } from "react"
import { ProAssistantDialog } from "./pro-dialog" // Updated dialog name
import { SubMenu } from "./sub-menu"

type HumanAssistantSelectorProps = {
  selectedAssistantId: string
  setSelectedAssistantId: (assistantId: string) => void
  className?: string
  isUserAuthenticated?: boolean
}

// Human & Personal Assistant Selector – warm, personal, and user-friendly!
export function HumanAssistantSelector({
  selectedAssistantId,
  setSelectedAssistantId,
  className,
  isUserAuthenticated = true,
}: HumanAssistantSelectorProps) {
  const { assistants, isLoading: isLoadingAssistants, favoriteAssistants } = useAssistant()
  const { isModelHidden } = useUserPreferences() // Note: Update to isAssistantHidden if customized

  const currentAssistant = assistants.find((assistant) => assistant.id === selectedAssistantId)
  const currentProvider = PROVIDERS.find(
    (provider) => provider.id === currentAssistant?.icon
  )
  const isMobile = useBreakpoint(768)

  const [hoveredAssistant, setHoveredAssistant] = useState<string | null>(null)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [isProDialogOpen, setIsProDialogOpen] = useState(false)
  const [selectedProAssistant, setSelectedProAssistant] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  const searchInputRef = useRef<HTMLInputElement>(null)

  useKeyShortcut(
    (e) => (e.key === "p" || e.key === "P") && e.metaKey && e.shiftKey,
    () => {
      if (isMobile) {
        setIsDrawerOpen((prev) => !prev)
      } else {
        setIsDropdownOpen((prev) => !prev)
      }
    }
  )

  // Assistant personification
  const renderAssistantItem = (assistant: AssistantConfig) => {
    const isLocked = !assistant.accessible
    const provider = PROVIDERS.find((provider) => provider.id === assistant.icon)
    return (
      <div
        key={assistant.id}
        className={cn(
          "flex w-full items-center justify-between px-3 py-2",
          selectedAssistantId === assistant.id && "bg-accent"
        )}
        onClick={() => {
          if (isLocked) {
            setSelectedProAssistant(assistant.id)
            setIsProDialogOpen(true)
            return
          }
          setSelectedAssistantId(assistant.id)
          if (isMobile) {
            setIsDrawerOpen(false)
          } else {
            setIsDropdownOpen(false)
          }
        }}
      >
        <div className="flex items-center gap-3">
          {provider?.icon && <provider.icon className="size-5" />}
          <div className="flex flex-col gap-0">
            <span className="text-sm">{assistant.name}</span>
            <small className="text-xs text-muted-foreground">Your personal companion</small>
          </div>
        </div>
        {isLocked && (
          <div className="border-input bg-accent text-muted-foreground flex items-center gap-0.5 rounded-full border px-1.5 py-0.5 text-[10px] font-medium">
            <StarIcon className="size-2" />
            <span>Premium Assistant</span>
          </div>
        )}
      </div>
    )
  }

  const hoveredAssistantData = assistants.find((assistant) => assistant.id === hoveredAssistant)
  const filteredAssistants = filterAndSortAssistants(
    assistants,
    favoriteAssistants || [],
    searchQuery,
    isModelHidden // Update to isAssistantHidden if customized
  )
  const trigger = (
    <Button
      variant="outline"
      className={cn("dark:bg-secondary justify-between", className)}
      disabled={isLoadingAssistants}
    >
      <div className="flex items-center gap-2">
        <span>{currentAssistant?.name || "Choose your assistant"}</span>
      </div>
      <CaretDownIcon className="size-4 opacity-50" />
    </Button>
  )

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation()
    setSearchQuery(e.target.value)
  }

  if (!isUserAuthenticated) {
    return (
      <Popover>
        <Tooltip>
          <TooltipTrigger asChild>
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="secondary"
                className={cn(
                  "border-border dark:bg-secondary text-accent-foreground h-9 w-auto border bg-transparent",
                  className
                )}
                type="button"
              >
                {currentAssistant?.name}
                <CaretDownIcon className="size-4" />
              </Button>
            </PopoverTrigger>
          </TooltipTrigger>
          <TooltipContent>Select your assistant 😊</TooltipContent>
        </Tooltip>
        <PopoverContentAuth />
      </Popover>
    )
  }

  if (isMobile) {
    return (
      <>
        <ProAssistantDialog
          isOpen={isProDialogOpen}
          setIsOpen={setIsProDialogOpen}
          currentAssistant={selectedProAssistant || ""}
        />
        <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
          <DrawerTrigger asChild>{trigger}</DrawerTrigger>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Choose Your Personal Assistant</DrawerTitle>
            </DrawerHeader>
            <div className="px-4 pb-2">
              <div className="relative">
                <MagnifyingGlassIcon className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search assistants..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
            <div className="flex h-full flex-col space-y-0 overflow-y-auto px-4 pb-6">
              {isLoadingAssistants ? (
                <div className="flex h-full flex-col items-center justify-center p-6 text-center">
                  <p className="text-muted-foreground mb-2 text-sm">
                    Loading your available assistants...
                  </p>
                </div>
              ) : filteredAssistants.length > 0 ? (
                filteredAssistants.map((assistant) => renderAssistantItem(assistant))
              ) : (
                <div className="flex h-full flex-col items-center justify-center p-6 text-center">
                  <p className="text-muted-foreground mb-2 text-sm">
                    Oops, no assistants matched your search.
                  </p>
                  <a
                    href="https://github.com/ibelick/zola/issues/new?title=Assistant%20Request%3A%20"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground text-sm underline"
                  >
                    Request a new personal assistant
                  </a>
                </div>
              )}
            </div>
          </DrawerContent>
        </Drawer>
      </>
    )
  }

  return (
    <div>
      <ProAssistantDialog
        isOpen={isProDialogOpen}
        setIsOpen={setIsProDialogOpen}
        currentAssistant={selectedProAssistant || ""}
      />
      <Tooltip>
        <DropdownMenu
          open={isDropdownOpen}
          onOpenChange={(open) => {
            setIsDropdownOpen(open)
            if (!open) {
              setHoveredAssistant(null)
              setSearchQuery("")
            } else {
              if (selectedAssistantId) setHoveredAssistant(selectedAssistantId)
            }
          }}
        >
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>Switch assistant ⌘⇧P</TooltipContent>
          <DropdownMenuContent
            className="flex h-[320px] w-[300px] flex-col space-y-0.5 overflow-visible p-0"
            align="start"
            sideOffset={4}
            forceMount
            side="top"
          >
            <div className="bg-background sticky top-0 z-10 rounded-t-md border-b px-0 pt-0 pb-0">
              <div className="relative">
                <MagnifyingGlassIcon className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search assistants..."
                  className="dark:bg-popover rounded-b-none border border-none pl-8 shadow-none focus-visible:ring-0"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onClick={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  onKeyDown={(e) => e.stopPropagation()}
                />
              </div>
            </div>
            <div className="flex h-full flex-col space-y-0 overflow-y-auto px-1 pt-0 pb-0">
              {isLoadingAssistants ? (
                <div className="flex h-full flex-col items-center justify-center p-6 text-center">
                  <p className="text-muted-foreground mb-2 text-sm">
                    Loading your available assistants...
                  </p>
                </div>
              ) : filteredAssistants.length > 0 ? (
                filteredAssistants.map((assistant) => {
                  const isLocked = !assistant.accessible
                  const provider = PROVIDERS.find(
                    (provider) => provider.id === assistant.icon
                  )
                  return (
                    <DropdownMenuItem
                      key={assistant.id}
                      className={cn(
                        "flex w-full items-center justify-between px-3 py-2",
                        selectedAssistantId === assistant.id && "bg-accent"
                      )}
                      onSelect={() => {
                        if (isLocked) {
                          setSelectedProAssistant(assistant.id)
                          setIsProDialogOpen(true)
                          return
                        }
                        setSelectedAssistantId(assistant.id)
                        setIsDropdownOpen(false)
                      }}
                      onFocus={() => {
                        if (isDropdownOpen) {
                          setHoveredAssistant(assistant.id)
                        }
                      }}
                      onMouseEnter={() => {
                        if (isDropdownOpen) {
                          setHoveredAssistant(assistant.id)
                        }
                      }}
                    >
                      <div className="flex items-center gap-3">
                        {provider?.icon && <provider.icon className="size-5" />}
                        <div className="flex flex-col gap-0">
                          <span className="text-sm">{assistant.name}</span>
                        </div>
                      </div>
                      {isLocked && (
                        <div className="border-input bg-accent text-muted-foreground flex items-center gap-0.5 rounded-full border px-1.5 py-0.5 text-[10px] font-medium">
                          <span>Premium Assistant</span>
                        </div>
                      )}
                    </DropdownMenuItem>
                  )
                })
              ) : (
                <div className="flex h-full flex-col items-center justify-center p-6 text-center">
                  <p className="text-muted-foreground mb-1 text-sm">
                    No assistants found.
                  </p>
                  <a
                    href="https://github.com/ibelick/zola/issues/new?title=Assistant%20Request%3A%20"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground text-sm underline"
                  >
                    Request a new personal assistant
                  </a>
                </div>
              )}
            </div>  
          </DropdownMenuContent>
        </DropdownMenu>
      </Tooltip>
    </div>
  )
}
