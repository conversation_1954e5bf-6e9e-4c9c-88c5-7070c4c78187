import * as React from "react"
import type { SVGProps } from "react"

const Icon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={64}
    height={64}
    viewBox="0 0 64 64"
    fill="none"
    {...props}
  >
    <g clipPath="url(#claude)">
      <path
        fill="#D97757"
        d="m12.557 42.547 12.587-7.059.213-.613-.213-.342h-.61l-2.107-.128-7.195-.194-6.237-.259-6.043-.325-1.523-.323L0 31.424l.147-.939 1.28-.856 1.829.16 4.053.275 6.075.421 4.405.259 6.531.68h1.037l.147-.419-.357-.261-.275-.259-6.288-4.256-6.805-4.501-3.563-2.592-1.93-1.31-.971-1.231-.422-2.688 1.75-1.926 2.349.16.6.163 2.381 1.83 5.088 3.935 6.643 4.888.973.811.387-.275.05-.194-.437-.731-3.613-6.523-3.856-6.64-1.717-2.752-.454-1.65a8 8 0 0 1-.277-1.944L16.755.357 17.856 0l2.656.357 1.12.971 1.653 3.77 2.672 5.945 4.147 8.08 1.216 2.394.648 2.219.243.68h.421v-.39l.341-4.549.632-5.586.614-7.187.213-2.027 1.003-2.426L37.427.939l1.557.746 1.28 1.827-.179 1.184-.762 4.936-1.491 7.741-.97 5.179h.565l.648-.645 2.626-3.483 4.406-5.504 1.946-2.187 2.267-2.41 1.459-1.15h2.754l2.027 3.011-.907 3.11-2.837 3.591-2.35 3.046-3.37 4.533-2.107 3.627.195.293.501-.053 7.616-1.616 4.115-.747 4.91-.84 2.22 1.035.243 1.053-.874 2.152-5.251 1.296-6.157 1.232-9.171 2.168-.112.08.13.163 4.131.389 1.766.096h4.325l8.053.6 2.107 1.392L64 38.485l-.21 1.294-3.24 1.653-4.374-1.037-10.21-2.427-3.5-.877h-.485v.293l2.915 2.848 5.35 4.827 6.69 6.213.339 1.541-.859 1.214-.907-.131-5.88-4.419-2.269-1.992-5.136-4.32h-.341v.454l1.184 1.73 6.253 9.39.325 2.88-.453.941-1.621.568-1.782-.325-3.664-5.134-3.773-5.778-3.048-5.182-.373.214-1.798 19.344-.842.986-1.944.747-1.619-1.23-.859-1.991.859-3.936 1.037-5.131.84-4.08.763-5.067.453-1.685-.032-.112-.373.048-3.824 5.245-5.813 7.854-4.603 4.92-1.104.437-1.912-.987.179-1.765 1.069-1.57 6.368-8.097 3.84-5.018 2.48-2.896-.016-.422h-.147L11.02 49.493l-3.014.39-1.298-1.216.162-1.99.616-.648 5.088-3.498z"
      />
    </g>
    <defs>
      <clipPath id="claude">
        <path fill="#fff" d="M0 0h64v64H0z" />
      </clipPath>
    </defs>
  </svg>
)
export default Icon
