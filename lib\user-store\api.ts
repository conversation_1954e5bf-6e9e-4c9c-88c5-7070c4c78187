import { toast } from "@/components/ui/toast"
import type { UserProfile } from "@/lib/user/types"

/**
 * Fetch the current user profile from your backend API.
 * This assumes your API reads secure httpOnly cookies for authentication.
 */
export async function fetchUserProfile(): Promise<UserProfile | null> {
  try {
    const res = await fetch("/api/auth/me", {
      credentials: "include", // important to send cookies
    })
    if (!res.ok) {
      console.error("Failed to fetch user profile from API")
      return null
    }
    const data = (await res.json()) as UserProfile
    if (!data || data.anonymous) return null
    return data
  } catch (error) {
    console.error("Error fetching user profile:", error)
    return null
  }
}

/**
 * Update the user profile by calling your backend API.
 * Adjust endpoint and payload as per your backend API design.
 */
export async function updateUserProfile(
  id: string,
  updates: Partial<UserProfile>
): Promise<boolean> {
  try {
    const res = await fetch(`/api/users/${id}`, {
      method: "PUT", // or PATCH depending on your API
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(updates),
    })
    if (!res.ok) {
      console.error("Failed to update user profile via API")
      return false
    }
    return true
  } catch (error) {
    console.error("Error updating user profile:", error)
    return false
  }
}

/**
 * Sign out user by calling your backend API sign-out route if any.
 * Adjust endpoint as per your backend implementation.
 */
export async function signOutUser(): Promise<boolean> {
  try {
    const res = await fetch("/api/auth/signout", {
      method: "POST",
      credentials: "include",
    })
    if (!res.ok) {
      const errorData = await res.json().catch(() => null)
      toast({
        title: errorData?.message || "Failed to sign out",
        status: "error",
      })
      return false
    }
    return true
  } catch (error) {
    toast({
      title: "Sign out failed due to network error",
      status: "error",
    })
    return false
  }
}

/**
 * Stub for subscription to user updates.
 * Replace this with your real-time subscription using WebSockets, SSE, or polling if required.
 */
export function subscribeToUserUpdates(
  userId: string,
  onUpdate: (newData: Partial<UserProfile>) => void
) {
  // You will need to implement this depending on your app's real-time technology.
  // For now, return a no-op unsubscribe function to match interface.
  return () => {}
}
