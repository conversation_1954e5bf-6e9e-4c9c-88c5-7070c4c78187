import { openproviders } from "@/lib/openproviders"
import { ModelConfig } from "../types"

const grokModels: ModelConfig[] = [
  {
    id: "grok-2",
    name: "Grok 2",
    provider: "xAI",
    providerId: "xai",
    modelFamily: "Grok",
    baseProviderId: "xai",
    description:
      "Second-generation model developed by xAI, designed for reasoning and general tasks.",
    tags: ["reasoning", "conversational", "general-purpose"],
    contextWindow: 128000,
    inputCost: 2.0,
    outputCost: 10.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://x.ai",
    apiDocs: "https://docs.x.ai/docs/models",
    modelPage: "https://x.ai/news/grok-2",
    releasedAt: "2024-01-21",
    icon: "xai",
    apiSdk: (apiKey?: string) => openproviders("grok-2", undefined, apiKey),
  },
  {
    id: "grok-2-vision",
    name: "Grok 2 Vision",
    provider: "xAI",
    providerId: "xai",
    modelFamily: "Grok",
    baseProviderId: "xai",
    description: "Vision-capable variant of Grok 2 for multimodal use.",
    tags: ["vision", "multimodal", "reasoning"],
    contextWindow: 128000,
    inputCost: 2.5,
    outputCost: 12.5,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://x.ai",
    apiDocs: "https://docs.x.ai/docs/models",
    modelPage: "https://x.ai/news/grok-2",
    icon: "xai",
    apiSdk: (apiKey?: string) =>
      openproviders("grok-2-vision", undefined, apiKey),
  },
  {
    id: "grok-3",
    name: "Grok 3 Beta",
    provider: "xAI",
    providerId: "xai",
    modelFamily: "Grok",
    baseProviderId: "xai",
    description: "Early beta of Grok 3 with improved performance and speed.",
    tags: ["beta", "next-gen", "reasoning", "experimental"],
    contextWindow: 200000,
    inputCost: 3.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://x.ai",
    apiDocs: "https://docs.x.ai/docs/models",
    releasedAt: "2024-05-01",
    icon: "xai",
    apiSdk: (apiKey?: string) => openproviders("grok-3", undefined, apiKey),
  },
  {
    id: "grok-3-fast",
    name: "Grok 3 Fast Beta",
    provider: "xAI",
    providerId: "xai",
    modelFamily: "Grok",
    baseProviderId: "xai",
    description:
      "Beta version of Grok 3 optimized for lower latency responses.",
    tags: ["beta", "fast", "optimized", "experimental"],
    contextWindow: 128000,
    inputCost: 2.0,
    outputCost: 8.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://x.ai",
    apiDocs: "https://docs.x.ai/docs/models",
    releasedAt: "2024-05-01",
    icon: "xai",
    apiSdk: (apiKey?: string) =>
      openproviders("grok-3-fast", undefined, apiKey),
  },
  {
    id: "grok-3-mini",
    name: "Grok 3 Mini Beta",
    provider: "xAI",
    providerId: "xai",
    modelFamily: "Grok",
    baseProviderId: "xai",
    description: "Small variant of Grok 3 for lighter usage scenarios.",
    tags: ["beta", "mini", "lightweight", "cheap"],
    contextWindow: 64000,
    inputCost: 0.5,
    outputCost: 2.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Low",
    website: "https://x.ai",
    apiDocs: "https://docs.x.ai/docs/models",
    icon: "xai",
    apiSdk: (apiKey?: string) =>
      openproviders("grok-3-mini", undefined, apiKey),
  },
  {
    id: "grok-3-mini-fast",
    name: "Grok 3 Mini Fast Beta",
    provider: "xAI",
    providerId: "xai",
    modelFamily: "Grok",
    baseProviderId: "xai",
    description:
      "Faster version of Grok 3 Mini, ideal for responsive applications.",
    tags: ["beta", "mini", "fast", "responsive", "cheap"],
    contextWindow: 32000,
    inputCost: 0.25,
    outputCost: 1.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Low",
    website: "https://x.ai",
    apiDocs: "https://docs.x.ai/docs/models",
    icon: "xai",
    apiSdk: (apiKey?: string) =>
      openproviders("grok-3-mini-fast", undefined, apiKey),
  },
]

export { grokModels }
