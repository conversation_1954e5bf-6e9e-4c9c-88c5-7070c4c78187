import { ModelConfig } from "../types"

const llamaModels: ModelConfig[] = [
  {
    id: "llama-4-scout-groq",
    name: "Llama 4 Scout (Groq)",
    provider: "Meta",
    providerId: "meta",
    modelFamily: "Llama 4",
    baseProviderId: "meta",
    description:
      "Groq-hosted Llama 4 Scout variant optimized for ultra-fast inference.",
    tags: ["groq-hosted", "fast", "next-gen", "inference"],
    contextWindow: 131000,
    inputCost: 0.0,
    outputCost: 0.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: true,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://llama.meta.com",
    apiDocs: "https://docs.groq.com",
    modelPage: "https://ai.meta.com/llama/",
    icon: "llama",
  },
  {
    id: "llama-3-3-70b-groq",
    name: "Llama 3.3 70B (Groq)",
    provider: "Meta",
    providerId: "meta",
    modelFamily: "Llama 3",
    baseProviderId: "meta",
    description:
      "Groq-hosted Llama 3.3 with enhanced 70B weights and large context.",
    tags: ["groq-hosted", "large", "70b", "enhanced"],
    contextWindow: 128000,
    inputCost: 0.0,
    outputCost: 0.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: true,
    speed: "Fast",
    intelligence: "High",
    website: "https://llama.meta.com",
    apiDocs: "https://docs.groq.com",
    modelPage: "https://ai.meta.com/llama/",
    releasedAt: "2024-12-06",
    icon: "llama",
  },
  {
    id: "llama-3-1-8b-groq",
    name: "Llama 3.1 8B (Groq)",
    provider: "Meta",
    providerId: "meta",
    modelFamily: "Llama 3",
    baseProviderId: "meta",
    description:
      "8B version of Llama 3.1, tuned for Groq's low-latency execution.",
    tags: ["groq-hosted", "8b", "lightweight", "fast"],
    contextWindow: 128000,
    inputCost: 0.0,
    outputCost: 0.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: true,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://llama.meta.com",
    apiDocs: "https://docs.groq.com",
    modelPage: "https://ai.meta.com/llama/",
    releasedAt: "2024-07-23",
    icon: "llama",
  },
  {
    id: "llama-3-70b-groq",
    name: "Llama 3 70B (Groq)",
    provider: "Meta",
    providerId: "meta",
    modelFamily: "Llama 3",
    baseProviderId: "meta",
    description:
      "Early Llama 3 70B release on Groq with limited context window.",
    tags: ["groq-hosted", "70b", "early-release", "limited-context"],
    contextWindow: 8000,
    inputCost: 0.0,
    outputCost: 0.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: true,
    speed: "Medium",
    intelligence: "High",
    website: "https://llama.meta.com",
    apiDocs: "https://docs.groq.com",
    modelPage: "https://ai.meta.com/llama/",
    releasedAt: "2024-04-18",
    icon: "llama",
  },
  {
    id: "llama-3-1-405b-together",
    name: "Llama 3.1 405B (Together AI)",
    provider: "Meta",
    providerId: "meta",
    modelFamily: "Llama 3",
    baseProviderId: "meta",
    description:
      "Massive 405B Llama model served by Together AI with deep reasoning.",
    tags: ["together-hosted", "405b", "massive", "reasoning"],
    contextWindow: 8000,
    inputCost: 5.0,
    outputCost: 15.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: true,
    speed: "Slow",
    intelligence: "High",
    website: "https://llama.meta.com",
    apiDocs: "https://docs.together.ai",
    modelPage: "https://ai.meta.com/llama/",
    releasedAt: "2024-07-23",
    icon: "llama",
  },
]

export { llamaModels }
