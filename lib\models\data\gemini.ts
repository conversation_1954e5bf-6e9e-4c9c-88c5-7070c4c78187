import { openproviders } from "@/lib/openproviders"
import { ModelConfig } from "../types"

const geminiModels: ModelConfig[] = [
  {
    id: "gemini-2.5-flash-lite",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    provider: "Google",
    providerId: "google",
    modelFamily: "Gemini",
    baseProviderId: "google",
    description: "Multimodal flagship model with strong reasoning and code.",
    tags: ["multimodal", "vision", "reasoning", "flagship", "code"],
    contextWindow: 2000000,
    inputCost: 1.25,
    outputCost: 5.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://gemini.google.com",
    apiDocs: "https://ai.google.dev/api/docs",
    modelPage: "https://deepmind.google/technologies/gemini",
    releasedAt: "2024-02-15",
    icon: "gemini",
    apiSdk: (apiKey?: string) =>
      openproviders("gemini-2.5-flash-lite", undefined, apiKey),
  },
  {
    id: "gemini-2.5-flash",
    name: "Assesstant",
    provider: "Google",
    providerId: "google",
    modelFamily: "Gemini",
    baseProviderId: "google",
    description: "Gemini 2 Flash model tuned for ultra-low latency tasks.",
    tags: ["fast", "multimodal", "next-gen"],
    contextWindow: 1000000,
    inputCost: 0.075,
    outputCost: 0.3,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: true,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://gemini.google.com",
    apiDocs: "https://ai.google.dev/api/docs",
    modelPage: "https://deepmind.google/technologies/gemini",
    releasedAt: "2024-12-11",
    icon: "gemini",
    apiSdk: (apiKey?: string) =>
      openproviders("gemini-2.5-flash", undefined, apiKey),
  }
]

export { geminiModels }
