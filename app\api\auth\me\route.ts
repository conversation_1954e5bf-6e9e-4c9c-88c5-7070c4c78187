import { NextRequest, NextResponse } from "next/server"
import jwt from "jsonwebtoken"
import mysql from "mysql2/promise"

const JWT_SECRET = "705c083b6e26795fa47f2ff56c712171db00bd86c557d4de6df79e157a3d3211"

const pool = mysql.createPool({
  host: process.env.MYSQL_HOST!,
  user: process.env.MYSQL_USER!,
  password: process.env.MYSQL_PASSWORD!,
  database: process.env.MYSQL_DATABASE!,
  waitForConnections: true,
  connectionLimit: 10,
})

export async function GET(request: NextRequest) {
  const cookie = request.cookies.get("token")  // read cookie from request
  const token = cookie?.value

  if (!token) {
    return NextResponse.json(
      { error: "Unauthorized: No token found" },
      { status: 401 }
    )
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as {
      userId: number
      username: string
      phoneNumber: string
      iat: number
      exp: number
    }

    // Fetch user info from MySQL (esp. users.pfp)
    const [rows] = await pool.query(
      "SELECT pfp FROM users WHERE id = ? LIMIT 1",
      [decoded.userId]
    )
    const userRow = Array.isArray(rows) ? rows[0] : null

    const user = {
      id: decoded.userId,
      display_name: decoded.username,
      phone_number: decoded.phoneNumber,
      profile_image: userRow?.pfp ?? "",
    }

    return NextResponse.json(user)
  } catch (error) {
    return NextResponse.json(
      { error: "Unauthorized: Invalid token or DB error" },
      { status: 401 }
    )
  }
}
