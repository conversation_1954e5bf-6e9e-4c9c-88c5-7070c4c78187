"use client";
import { Switch } from "@/components/ui/switch"; // Assuming Shadcn/UI
import { motion } from "framer-motion";
import { Mic, X, Settings } from "lucide-react";
import { useEffect, useState, useRef } from "react";
import axios from 'axios'; // Axios आयात करें

export default function VoiceScreen({ onClose }) {
  const [listening, setListening] = useState(false);
  const [message, setMessage] = useState("नमस्ते, आप कैसे हैं?");
  const recognitionRef = useRef(null);
  const selectedVoiceRef = useRef(null);
  const [voiceReady, setVoiceReady] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  // Preference states
  const [subtitlesEnabled, setSubtitlesEnabled] = useState(false);
  const [voiceTone, setVoiceTone] = useState("personal-assistant"); // 'human' or 'personal-assistant'
  const [selectedLanguage, setSelectedLanguage] = useState("hi-IN"); // Language code (e.g., 'hi-IN', 'en-US')
  // Voice level detection states
  const [volume, setVolume] = useState(0);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const dataArrayRef = useRef(null);
  const animationFrameRef = useRef(null);
  const streamRef = useRef(null);

  // NEW: Flag to track if we've auto-started on mount
  const hasAutoStarted = useRef(false);

  // Load voices and set up speech synthesis (unchanged)
  useEffect(() => {
    if (!("speechSynthesis" in window)) {
      console.warn("SpeechSynthesis not supported in this browser.");
      return;
    }
    const loadVoices = () => {
      const voices = window.speechSynthesis.getVoices();
      if (voices.length > 0) {
        const suitableVoice = voices.find((v) => v.lang === selectedLanguage);
        if (suitableVoice) {
          selectedVoiceRef.current = suitableVoice;
          setVoiceReady(true);
          return true;
        }
      }
      return false;
    };
    loadVoices(); // Initial load
    window.speechSynthesis.onvoiceschanged = loadVoices; // Event listener
    let retries = 0;
    const retryInterval = setInterval(() => {
      if (voiceReady || retries > 10) {
        clearInterval(retryInterval);
        return;
      }
      if (loadVoices()) {
        setVoiceReady(true);
      }
      retries++;
    }, 300);
    return () => clearInterval(retryInterval);
  }, [selectedLanguage]);

  // Speak function with tone adjustments (unchanged)
  const speakText = (text) => {
    window.speechSynthesis.cancel(); // Cancel ongoing speech
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = selectedLanguage;
    utterance.voice = selectedVoiceRef.current;
    utterance.pitch = voiceTone === "human" ? 1.2 : 1;
    utterance.rate = voiceTone === "human" ? 0.9 : 1;
    // NEW: Add an event listener to restart recognition after speech ends
    utterance.onend = () => {
      if (!listening) {
        handleMicClick(); // Auto-restart recognition after speaking
      }
    };
    window.speechSynthesis.speak(utterance);
  };

  // Setup Speech Recognition (UPDATED: Added continuous: true for better flow, but kept interimResults false)
  useEffect(() => {
    if ("webkitSpeechRecognition" in window || "SpeechRecognition" in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      recognition.lang = selectedLanguage;
      recognition.interimResults = false;
      recognition.continuous = true; // UPDATED: Set to true for continuous listening, but we'll manage restarts manually
      recognition.onresult = async (event) => {
        const transcript = event.results[0].transcript;
        setListening(false);
        stopVoiceLevelDetection();
        // Get location (unchanged)
        let location = null;
        try {
          const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, { enableHighAccuracy: true });
          });
          location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          };
        } catch (error) {
          console.error("Location error:", error);
        }
        // API call (unchanged)
        const apiUrl = "/api/voicechat";
        try {
          const res = await axios.post(apiUrl, {
            message: transcript,
            user_id: "213",
            voiceTone: voiceTone,
            location: location
          }, {
            headers: { "Content-Type": "application/json" }
          });
          const apiData = res.data;
          const responseText = apiData?.data?.response || "No response available.";
          if (typeof responseText === 'string' && responseText.trim() !== '') {
            setMessage(responseText);
            speakText(responseText); // Speak the response
          } else {
            const fallbackMsg = selectedLanguage.startsWith("hi") ? "कोई प्रतिक्रिया उपलब्ध नहीं।" : "No response available.";
            setMessage(fallbackMsg);
            speakText(fallbackMsg);
          }
        } catch (error) {
          console.error("API error:", error);
          const errorMsg = selectedLanguage.startsWith("hi") ? "API कॉल विफल रही।" : "API call failed.";
          setMessage(errorMsg);
          speakText(errorMsg);
        }
      };
      recognition.onerror = (event) => {
        console.error("Recognition error:", event.error);
        setListening(false);
        stopVoiceLevelDetection();
      };
      recognition.onend = () => {
        setListening(false);
        stopVoiceLevelDetection();
      };
      recognitionRef.current = recognition;
    } else {
      console.warn("SpeechRecognition not supported in this browser.");
    }
  }, [selectedLanguage, voiceTone]);

  // Voice level detection functions (unchanged)
  const startVoiceLevelDetection = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      source.connect(analyserRef.current);
      dataArrayRef.current = new Uint8Array(analyserRef.current.frequencyBinCount);
      const updateVolume = () => {
        analyserRef.current.getByteFrequencyData(dataArrayRef.current);
        const avg = dataArrayRef.current.reduce((sum, val) => sum + val, 0) / dataArrayRef.current.length;
        setVolume(avg / 255);
        animationFrameRef.current = requestAnimationFrame(updateVolume);
      };
      updateVolume();
    } catch (error) {
      console.error("Voice level detection error:", error);
    }
  };

  const stopVoiceLevelDetection = () => {
    if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
    if (streamRef.current) streamRef.current.getTracks().forEach((track) => track.stop());
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close().catch((err) => console.error("Close error:", err));
    }
    setVolume(0);
  };

  // Handle mic click (UPDATED: Added logic to speak current message only if not listening)
  const handleMicClick = () => {
    if (!recognitionRef.current) return;
    if (!listening) {
      setListening(true);
      recognitionRef.current.start();
      startVoiceLevelDetection();
      // UPDATED: Speak only if this is the initial trigger (avoids repeating during loop)
      if (!hasAutoStarted.current) {
        speakText(message); // Initial speech
      }
    } else {
      recognitionRef.current.stop();
      setListening(false);
      stopVoiceLevelDetection();
    }
  };

  // Initial silent utterance and auto-start (UPDATED: Auto-start recognition after greeting)
  useEffect(() => {
    if (voiceReady && !hasAutoStarted.current) {
      const silent = new SpeechSynthesisUtterance("");
      silent.volume = 0;
      window.speechSynthesis.speak(silent);
      setTimeout(() => {
        hasAutoStarted.current = true; // Mark as started
        handleMicClick(); // NEW: Auto-start recognition after silent utterance
      }, 500);
    }
  }, [voiceReady]);

  // Download handler and other UI components (unchanged)
  const handleDownload = () => {
    const blob = new Blob([message], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "nityasha-transcript.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Reusable components (unchanged)
  const ToggleSwitch = ({ checked, onChange, label }) => (
    <label className="flex items-center cursor-pointer">
      <input type="checkbox" checked={checked} onChange={onChange} className="sr-only" aria-label={label} />
      <div className="relative w-10 h-6 bg-gray-300 rounded-full shadow-inner">
        <div className={`absolute top-1 left-1 w-4 h-4 bg-white rounded-full transition-transform ${checked ? "translate-x-4" : ""}`} />
      </div>
    </label>
  );

  const RadioButton = ({ checked, onChange, label }) => (
    <label className="flex items-center justify-between w-full cursor-pointer p-2 rounded-md">
      <span className="text-[19px] font-medium">{label}</span>
      <input
        type="radio"
        checked={checked}
        onChange={onChange}
        className="w-5 h-5 rounded-full border-2 border-gray-400 appearance-none checked:bg-blue-500 checked:border-blue-500"
        aria-label={label}
      />
    </label>
  );

  const PreferenceSection = ({ title, children }) => (
    <section className="mb-6">
      <h2 className="text-[19px] font-medium mb-3">{title}</h2>
      {children}
    </section>
  );

  // JSX return (unchanged, except ensure mic button still works for manual control if needed)
  return (
    <motion.div
      className="fixed inset-0 bg-[#FBFAF5] dark:bg-black flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Main content */}
      <div className="relative flex flex-col items-center justify-center w-full h-full">
        <div className="absolute top-4 left-4 text-lg font-semibold">Nityasha</div>
        <button className="absolute top-4 right-4 z-10" onClick={() => setIsSidebarOpen(true)}>
          <Settings size={28} className="text-black dark:text-white" />
        </button>
        {/* Centered circle and message */}
        <div className="flex flex-col items-center justify-center">
          <div
            className={`relative w-40 h-40 rounded-full ${listening ? "bg-red-500 animate-pulse" : "bg-black"}`}
            style={{
              boxShadow: "0px 0px 250px 50px rgba(0, 208, 255, 0.25)",
              transform: `scale(${1 + volume})`,
              transition: "transform 0.1s ease-in-out",
            }}
          />
          {subtitlesEnabled && (
            <div className="absolute inset-0 flex items-center justify-center mt-[20rem] text-sm text-gray-900 dark:text-white font-[Poppins]">
              {message}
            </div>
          )}
        </div>
        <div className="absolute bottom-12 flex gap-[30px]">
          <button
            className={`w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-md ${listening ? "text-red-500 animate-pulse" : "text-black"}`}
            onClick={handleMicClick}
          >
            <Mic size={24} />
          </button>
          <button className="w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-md text-black" onClick={onClose}>
            <X size={24} />
          </button>
        </div>
      </div>
      {isSidebarOpen && (
        <div
          className="absolute inset-0 backdrop-blur-md pointer-events-auto z-20"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}
      {isSidebarOpen && (
        <motion.div
          className="absolute top-0 right-0 h-full w-[90%] bg-[#B2B2B2] shadow-lg overflow-y-auto rounded-tl-[41px] rounded-bl-[41px] z-30 md:w-[400px]"
          initial={{ x: "100%" }}
          animate={{ x: 0 }}
          exit={{ x: "100%" }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <header className="sticky top-0 flex justify-center items-center h-[80px] bg-[#B2B2B2] z-10 px-4">
            <h1 className="text-[28px] font-semibold">Nityasha Preferences</h1>
          </header>
          <div className="h-[calc(100vh-80px)] overflow-y-auto bg-[#E5E5E5] mx-1 rounded-t-[41px] pt-4 px-4 pb-4">
            <button
              className="w-full rounded-full bg-black mb-5 h-[60px] flex items-center justify-center text-white font-medium transition-colors"
              onClick={handleDownload}
              aria-label="Download transcript"
            >
              Download
            </button>
            <PreferenceSection title="Subtitles">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-[19px] font-medium">See the response transcript</p>
                  <span className="text-[#767676] text-sm">Toggle to enable/disable subtitles</span>
                </div>
                <ToggleSwitch
                  checked={subtitlesEnabled}
                  onChange={() => setSubtitlesEnabled(!subtitlesEnabled)}
                  label="Subtitles toggle"
                />
              </div>
            </PreferenceSection>
            <PreferenceSection title="Voice Tone">
              <RadioButton label="Human" checked={voiceTone === "human"} onChange={() => setVoiceTone("human")} />
              <RadioButton
                label="Personal Assistant"
                checked={voiceTone === "personal-assistant"}
                onChange={() => setVoiceTone("personal-assistant")}
              />
            </PreferenceSection>
            <PreferenceSection title="Language">
              <div className="flex flex-col">
                <label htmlFor="language-select" className="text-[19px] font-medium mb-2">
                  Select Language
                </label>
                <select
                  id="language-select"
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md bg-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="Select preferred language"
                >
                  <option value="hi-IN">Hindi (Current)</option>
                  <option value="en-US">English</option>
                  <option value="ta-IN">Tamil</option>
                </select>
              </div>
            </PreferenceSection>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}
