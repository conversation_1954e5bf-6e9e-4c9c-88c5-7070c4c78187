import { openproviders } from "@/lib/openproviders"
import { ModelConfig } from "../types"

const mistralModels: ModelConfig[] = [
  {
    id: "open-mistral-7b",
    name: "Mistral Codestral 25.01",
    provider: "Mistral",
    providerId: "mistral",
    baseProviderId: "mistral",
    modelFamily: "Codestral",
    description: "Open-weight code model focused on developer tasks.",
    tags: ["code", "open-source"],
    contextWindow: 32768,
    inputCost: 0.0,
    outputCost: 0.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: true,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://mistral.ai",
    apiDocs: "https://docs.mistral.ai/models",
    modelPage: "https://mistral.ai/news/mistral-codestral/",
    releasedAt: "2024-05-21",
    icon: "mistral",
    apiSdk: (apiKey?: string) =>
      openproviders("open-mistral-7b", undefined, apiKey),
  },
  {
    id: "ministral-3b-latest",
    name: "Ministral 3B",
    provider: "Mistral",
    providerId: "mistral",
    baseProviderId: "mistral",
    modelFamily: "Ministral",
    description: "Tiny model optimized for cost and small-scale inference.",
    tags: ["cheap", "open-source", "tiny"],
    contextWindow: 32768,
    inputCost: 0.0,
    outputCost: 0.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: false,
    audio: false,
    reasoning: false,
    openSource: true,
    speed: "Fast",
    intelligence: "Low",
    website: "https://mistral.ai",
    apiDocs: "https://docs.mistral.ai/models",
    modelPage: "https://mistral.ai/news/ministral/",
    icon: "mistral",
    apiSdk: (apiKey?: string) =>
      openproviders("ministral-3b-latest", undefined, apiKey),
  },
  {
    id: "ministral-8b-latest",
    name: "Ministral 8B",
    provider: "Mistral",
    providerId: "mistral",
    baseProviderId: "mistral",
    modelFamily: "Ministral",
    description: "Balanced small open-weight model with solid reasoning.",
    tags: ["open-source", "balanced"],
    contextWindow: 32768,
    inputCost: 0.0,
    outputCost: 0.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: true,
    speed: "Medium",
    intelligence: "Medium",
    website: "https://mistral.ai",
    apiDocs: "https://docs.mistral.ai/models",
    modelPage: "https://mistral.ai/news/ministral/",
    icon: "mistral",
    apiSdk: (apiKey?: string) =>
      openproviders("ministral-8b-latest", undefined, apiKey),
  },
  {
    id: "mistral-large-latest",
    name: "Mistral Large",
    provider: "Mistral",
    providerId: "mistral",
    baseProviderId: "mistral",
    modelFamily: "Mistral",
    description: "Mistral's top-tier model with advanced reasoning.",
    tags: ["reasoning", "flagship"],
    contextWindow: 128000,
    inputCost: 2.0,
    outputCost: 6.0,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://mistral.ai",
    apiDocs: "https://docs.mistral.ai/models",
    modelPage: "https://mistral.ai/news/mistral-large/",
    releasedAt: "2024-02-26",
    icon: "mistral",
    apiSdk: (apiKey?: string) =>
      openproviders("mistral-large-latest", undefined, apiKey),
  },
  {
    id: "mistral-small-latest",
    name: "Mistral Small",
    provider: "Mistral",
    providerId: "mistral",
    baseProviderId: "mistral",
    modelFamily: "Mistral",
    description: "Smaller hosted model for fast, general-purpose chat.",
    tags: ["fast", "general-purpose"],
    contextWindow: 32768,
    inputCost: 0.2,
    outputCost: 0.6,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://mistral.ai",
    apiDocs: "https://docs.mistral.ai/models",
    icon: "mistral",
    apiSdk: (apiKey?: string) =>
      openproviders("mistral-small-latest", undefined, apiKey),
  },
  {
    id: "mistral-small-2503",
    name: "Mistral Small 2503",
    provider: "Mistral",
    providerId: "mistral",
    baseProviderId: "mistral",
    modelFamily: "Mistral",
    description: "Versioned release of Mistral Small from March 2025.",
    tags: ["fast", "general-purpose", "versioned"],
    contextWindow: 32768,
    inputCost: 0.2,
    outputCost: 0.6,
    priceUnit: "per 1M tokens",
    vision: false,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: false,
    speed: "Fast",
    intelligence: "Medium",
    website: "https://mistral.ai",
    apiDocs: "https://docs.mistral.ai/models",
    releasedAt: "2025-03-01",
    icon: "mistral",
    apiSdk: (apiKey?: string) =>
      openproviders("mistral-small-latest", undefined, apiKey),
  },
  {
    id: "pixtral-12b-2409",
    name: "Pixtral 12B 2409",
    provider: "Mistral",
    providerId: "mistral",
    baseProviderId: "mistral",
    modelFamily: "Pixtral",
    description: "Open-weight mixture of experts model.",
    tags: ["open-source", "moe", "vision"],
    contextWindow: 32768,
    inputCost: 0.0,
    outputCost: 0.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: false,
    openSource: true,
    speed: "Medium",
    intelligence: "Medium",
    website: "https://mistral.ai",
    apiDocs: "https://docs.mistral.ai/models",
    modelPage: "https://mistral.ai/news/pixtral-12b/",
    releasedAt: "2024-09-01",
    icon: "mistral",
    apiSdk: (apiKey?: string) =>
      openproviders("pixtral-12b-2409", undefined, apiKey),
  },
  {
    id: "pixtral-large-latest",
    name: "Pixtral Large",
    provider: "Mistral",
    providerId: "mistral",
    baseProviderId: "mistral",
    modelFamily: "Pixtral",
    description: "High-capacity Pixtral variant for advanced use cases.",
    tags: ["vision", "multimodal", "large"],
    contextWindow: 128000,
    inputCost: 3.0,
    outputCost: 9.0,
    priceUnit: "per 1M tokens",
    vision: true,
    tools: true,
    audio: false,
    reasoning: true,
    openSource: false,
    speed: "Medium",
    intelligence: "High",
    website: "https://mistral.ai",
    apiDocs: "https://docs.mistral.ai/models",
    modelPage: "https://mistral.ai/news/pixtral-large/",
    icon: "mistral",
    apiSdk: (apiKey?: string) =>
      openproviders("pixtral-large-latest", undefined, apiKey),
  },
]

export { mistralModels }
